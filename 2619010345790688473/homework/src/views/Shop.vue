<template>
  <div class="shop-container">
    <div class="header">
      <div class="header-text">
        <span>web前端开发期末大作业</span>
        <span>2023级计算机237班张富  学号: 2375409295</span>
      </div>
    </div>
    <div class="content">
      <div class="products-grid">
        <div class="product-card" v-for="(product, index) in products" :key="index">
          <div class="product-image">
            <img :src="product.image" :alt="product.name" />
          </div>
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-price">{{ product.price }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import image1 from '../picture/1.jpg';
import image2 from '../picture/2.png';
import image3 from '../picture/3.jpg';
import image4 from '../picture/4.jpg';
import image5 from '../picture/5.png';
import image6 from '../picture/6.png';
import image7 from '../picture/7.webp';
import image8 from '../picture/8.webp';
import image9 from '../picture/9.png';

export default {
  name: 'Shop',
  data() {
    return {
      products: [
        { name: '名称:麻辣豆腐', price: '价格:68元', image: image1 },
        { name: '名称:糖醋大肠', price: '价格:58元', image: image2 },
        { name: '名称:葱油排骨', price: '价格:48元', image: image3 },
        { name: '名称:糖醋里脊', price: '价格:68元', image: image4 },
        { name: '名称:麻婆豆腐', price: '价格:18元', image: image5 },
        { name: '名称:毛式红烧肉', price: '价格:35元', image: image6 },
        { name: '名称:红烧茄子', price: '价格:16元', image: image7 },
        { name: '名称:白切鸡', price: '价格:38元', image: image8 },
        { name: '名称:佛跳墙', price: '价格:118元', image: image9 }
      ]
    };
  }
};
</script>

<style scoped>
.shop-container {
  padding: 0;
  width: 800px;
  margin: auto;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background-color: #ff9933;
  color: white;
  padding: 15px 20px;
  text-align: center;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.header-text span:first-child {
  font-size: 18px;
  font-weight: bold;
}

.header-text span:last-child {
  font-size: 14px;
  opacity: 0.9;
}

.content {
  padding: 20px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.product-card {
  background-color: white;
  border: 2px solid #ff6b6b;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 120px;
  overflow: hidden;
  border-radius: 6px;
  margin-bottom: 8px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.product-price {
  font-size: 13px;
  color: #666;
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shop-container {
    width: 100%;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .content {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
}
</style>
