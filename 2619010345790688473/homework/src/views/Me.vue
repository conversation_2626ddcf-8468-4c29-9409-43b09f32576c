<template>
  <div class="me-container">
    <div class="header">
      <div class="header-text">
        <span>web前端开发期末大作业</span>
        <span>2023级计算机237班张富  学号: **********</span>
      </div>
    </div>
    <div class="user-info">
      <div class="avatar-container">
        <img
          src="../asserts/1.png"
          alt="avatar"
          class="avatar-image"
        />
      </div>
      <div class="user-details">
        <div class="user-name">用户名称:小陈每天都要笑</div>
        <div class="user-account">用户账号:**********</div>
        <div class="status-btn">+状态</div>
      </div>
      <div class="qr-code">
        <img src="../asserts/erweima.png" alt="二维码" class="qr-image" />
      </div>
    </div>
    <div class="settings">
      <div class="setting-item" v-for="(item, index) in settingItems" :key="index">
        <div class="setting-content">
          <img :src="item.icon" :alt="item.name" class="setting-icon" />
          <span class="setting-text">{{ item.name }}</span>
        </div>
        <el-icon><ArrowRight /></el-icon>
      </div>
    </div>
  </div>
</template>

<script>
import { Setting, ArrowRight } from '@element-plus/icons-vue';
import img1 from '../asserts/1.png';
import img2 from '../asserts/2.jpeg';
import touxiang from '../asserts/3.png';
import erweima from '../asserts/4.png';
import banner1 from '../asserts/5.png';

export default {
  components: {
    Setting,
    ArrowRight
  },
  data() {
    return {
      settingItems: [
        { icon: img1, name: '关于' },
        { icon: img2, name: '服务' },
        { icon: touxiang, name: '收藏' },
        { icon: erweima, name: '帮助' },
        { icon: banner1, name: '设置' }
      ]
    };
  }
};
</script>

<style scoped>
.me-container {
  padding: 0;
  width: 800px;
  height: 900px;
  margin: auto;
  background-color: #f5f5f5;
}

.header {
  background-color: #ff9933;
  color: white;
  padding: 15px 20px;
  text-align: center;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.header-text span:first-child {
  font-size: 18px;
  font-weight: bold;
}

.header-text span:last-child {
  font-size: 14px;
  opacity: 0.9;
}

.user-info {
  display: flex;
  align-items: center;
  margin: 20px;
  background-color: white;
  padding: 25px;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.avatar-container {
  margin-right: 20px;
  margin-left: 10px;
}

.avatar-image {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  object-fit: cover;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.user-account {
  font-size: 14px;
  color: #666;
}

.status-btn {
  width: fit-content;
  margin-top: 10px;
  padding: 6px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 13px;
  color: #666;
  background-color: #f5f5f5;
  display: inline-block;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-btn:hover {
  background-color: #e8e8e8;
  border-color: #ccc;
}

.qr-code {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

.qr-image {
  width: 40px;
  height: 40px;
}

.settings {
  margin-top: 0;
  padding: 0 20px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background-color: white;
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.setting-item:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.setting-item .setting-content {
  display: flex;
  align-items: center;
}

.setting-item .setting-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.setting-item .setting-text {
  font-size: 16px;
  color: #333;
}
</style>
