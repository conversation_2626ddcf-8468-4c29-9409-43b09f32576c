#!/usr/bin/env python3
"""
K-means Clustering for Microarray Gene Expression Data
Implements K-means clustering with support for random or file-based initial centroids
"""

import sys
import random
import math

class KMeansClusterer:
    def __init__(self, k, max_iterations=50):
        self.k = k
        self.max_iterations = max_iterations
        self.data = []
        self.centroids = []
        self.clusters = []
        self.num_genes = 0
        self.num_experiments = 0
        
    def load_data(self, filename):
        """Load microarray data from tab-delimited file"""
        with open(filename, 'r') as f:
            lines = f.readlines()
        
        self.data = []
        for line in lines:
            if line.strip():
                values = [float(x) for x in line.strip().split('\t')]
                self.data.append(values)
        
        self.num_genes = len(self.data)
        self.num_experiments = len(self.data[0]) if self.data else 0
        
        print(f"Loaded {self.num_genes} genes with {self.num_experiments} experiments each")
    
    def load_centroids(self, filename):
        """Load initial centroids from file"""
        with open(filename, 'r') as f:
            lines = f.readlines()
        
        self.centroids = []
        for line in lines:
            if line.strip():
                values = [float(x) for x in line.strip().split('\t')]
                self.centroids.append(values)
        
        if len(self.centroids) != self.k:
            raise ValueError(f"Expected {self.k} centroids, but got {len(self.centroids)}")
        
        print(f"Loaded {len(self.centroids)} initial centroids from file")
    
    def initialize_random_centroids(self):
        """Initialize centroids randomly from the data points"""
        if not self.data:
            raise ValueError("No data loaded")
        
        # Randomly select k data points as initial centroids
        random.seed(42)  # For reproducible results
        selected_indices = random.sample(range(self.num_genes), self.k)
        
        self.centroids = []
        for idx in selected_indices:
            self.centroids.append(self.data[idx][:])  # Copy the data point
        
        print(f"Initialized {len(self.centroids)} random centroids")
    
    def euclidean_distance(self, point1, point2):
        """Calculate Euclidean distance between two points"""
        if len(point1) != len(point2):
            raise ValueError("Points must have same dimensionality")
        
        distance = 0.0
        for i in range(len(point1)):
            distance += (point1[i] - point2[i]) ** 2
        
        return math.sqrt(distance)
    
    def assign_clusters(self):
        """Assign each data point to the nearest centroid"""
        new_clusters = []
        
        for gene_idx in range(self.num_genes):
            gene_data = self.data[gene_idx]
            
            # Find the closest centroid
            min_distance = float('inf')
            closest_cluster = 0
            
            for centroid_idx in range(self.k):
                distance = self.euclidean_distance(gene_data, self.centroids[centroid_idx])
                if distance < min_distance:
                    min_distance = distance
                    closest_cluster = centroid_idx
            
            new_clusters.append(closest_cluster)
        
        return new_clusters
    
    def update_centroids(self):
        """Update centroids based on current cluster assignments"""
        new_centroids = []
        
        for cluster_idx in range(self.k):
            # Find all points assigned to this cluster
            cluster_points = []
            for gene_idx in range(self.num_genes):
                if self.clusters[gene_idx] == cluster_idx:
                    cluster_points.append(self.data[gene_idx])
            
            if not cluster_points:
                # If no points assigned to this cluster, keep the old centroid
                new_centroids.append(self.centroids[cluster_idx][:])
                print(f"Warning: Cluster {cluster_idx} has no assigned points")
            else:
                # Calculate mean of all points in this cluster
                new_centroid = [0.0] * self.num_experiments
                for point in cluster_points:
                    for dim in range(self.num_experiments):
                        new_centroid[dim] += point[dim]
                
                # Average the coordinates
                for dim in range(self.num_experiments):
                    new_centroid[dim] /= len(cluster_points)
                
                new_centroids.append(new_centroid)
        
        self.centroids = new_centroids
    
    def has_converged(self, old_clusters, new_clusters):
        """Check if the algorithm has converged (no changes in cluster assignments)"""
        if len(old_clusters) != len(new_clusters):
            return False
        
        for i in range(len(old_clusters)):
            if old_clusters[i] != new_clusters[i]:
                return False
        
        return True
    
    def run_kmeans(self):
        """Run the K-means clustering algorithm"""
        print(f"Starting K-means clustering with k={self.k}, max_iterations={self.max_iterations}")
        
        # Initialize clusters (all points start in cluster 0)
        self.clusters = [0] * self.num_genes
        
        for iteration in range(self.max_iterations):
            print(f"Iteration {iteration + 1}")
            
            # Store old cluster assignments
            old_clusters = self.clusters[:]
            
            # Assign points to clusters
            self.clusters = self.assign_clusters()
            
            # Check for convergence
            if self.has_converged(old_clusters, self.clusters):
                print(f"Converged after {iteration + 1} iterations")
                break
            
            # Update centroids
            self.update_centroids()
        else:
            print(f"Reached maximum iterations ({self.max_iterations}) without convergence")
        
        return self.clusters
    
    def write_output(self, filename):
        """Write cluster assignments to output file"""
        with open(filename, 'w') as f:
            for gene_idx in range(self.num_genes):
                # Gene numbers are 1-indexed, cluster numbers are 1-indexed
                f.write(f"{gene_idx + 1}\t{self.clusters[gene_idx] + 1}\n")
        
        print(f"Results written to {filename}")
    
    def print_cluster_summary(self):
        """Print summary of cluster assignments"""
        cluster_counts = [0] * self.k
        for cluster_id in self.clusters:
            cluster_counts[cluster_id] += 1
        
        print("\nCluster Summary:")
        for i in range(self.k):
            print(f"Cluster {i + 1}: {cluster_counts[i]} genes")

def main():
    if len(sys.argv) < 4:
        print("Usage: python kmeans.py <k> <data_file> <max_iterations> [centroids_file]")
        print("  k: number of clusters")
        print("  data_file: tab-delimited data file")
        print("  max_iterations: maximum number of iterations")
        print("  centroids_file: optional file with initial centroids")
        sys.exit(1)
    
    k = int(sys.argv[1])
    data_file = sys.argv[2]
    max_iterations = int(sys.argv[3])
    centroids_file = sys.argv[4] if len(sys.argv) > 4 else None
    
    # Create K-means clusterer
    clusterer = KMeansClusterer(k, max_iterations)
    
    # Load data
    try:
        clusterer.load_data(data_file)
    except Exception as e:
        print(f"Error loading data file: {e}")
        sys.exit(1)
    
    # Initialize centroids
    try:
        if centroids_file:
            clusterer.load_centroids(centroids_file)
        else:
            clusterer.initialize_random_centroids()
    except Exception as e:
        print(f"Error initializing centroids: {e}")
        sys.exit(1)
    
    # Run K-means
    clusters = clusterer.run_kmeans()
    
    # Write output
    clusterer.write_output("kmeans.out")
    clusterer.print_cluster_summary()

if __name__ == "__main__":
    main()
