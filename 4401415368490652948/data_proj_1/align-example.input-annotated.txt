AATGC         ;sequence A (length = # of columns)
AGGC          ;sequence B (length = # of rows)
0             ;0 = global,  1 = local
0 0 0 0       ;gap open penalty for A, gap extension for A, open for B, extend B
4             ;number of letters in alphabet for sequence A = NA
ATGC          ;letters in alphabet for sequence A
4             ;number of letters in alphabet for sequence B = NB
ATGC          ;letters in alphabet for sequence B
1 1 A A 1     ;
1 2 A T 0     ;These are the entries in the match matrix between
1 3 A G 0     ; alphabet for A and alphabet for B.  Letters appear
1 4 A C 0     ; in same order as lines 6 and 8.
2 1 T A 0     ; There are NA * NB entries in this matrix, and each
2 2 T T 1     ;    entry is a row.
2 3 T G 0     ; First col is row number in match matrix
2 4 T C 0     ; Second col is col number in match matrix
3 1 G A 0     ; Third col is letter from alphabet A  
3 2 G T 0     ; Fourth col is letter from alphabet B
3 3 G G 1     ; Fifth col is match score between the two letters
3 4 G C 0     ;
4 1 C A 0     ; Thus, this matrix gives positive score only for
4 2 C T 0     ; exact matches.  There are many other matrices
4 3 C G 0     ; also possible.
4 4 C C 1
