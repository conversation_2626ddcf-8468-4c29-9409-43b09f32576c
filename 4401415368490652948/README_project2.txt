# 项目2：K-means聚类算法

## 开发者信息
- **姓名**: [您的姓名]
- **学号**: [您的学号]

## 使用的编程语言和运行说明

### 编程语言
Python 3.x

### 运行环境要求
- Python 3.6或更高版本
- 无需额外的第三方库依赖

### 运行方法
```bash
python3 kmeans.py <k> <数据文件> <最大迭代次数> [质心文件]
```

**参数说明：**
- `k`: 聚类数量（整数）
- `数据文件`: 包含基因表达数据的制表符分隔文件
- `最大迭代次数`: 算法允许的最大迭代次数（整数）
- `质心文件`: 可选参数，包含初始质心的文件路径

**使用示例：**

1. 使用随机初始质心：
```bash
python3 kmeans.py 3 data_proj_2/yeast.dat.txt 50
```

2. 使用指定的初始质心文件：
```bash
python3 kmeans.py 3 data_proj_2/yeast.dat.txt 50 data_proj_2/test_centroids.txt
```

### 输入文件格式

1. **基因表达数据文件** (`yeast.dat.txt`):
   - 制表符分隔的文本文件
   - 每行代表一个基因，包含79个实验条件下的表达值
   - 共2467个基因

2. **质心文件** (`test_centroids.txt`，可选):
   - 制表符分隔的文本文件
   - K行×79列的矩阵，每行代表一个初始质心
   - 如果不提供此文件，程序将随机选择K个基因作为初始质心

### 输出文件
程序将结果输出到 `kmeans.out` 文件，格式为：
```
1    2
2    1
3    3
4    1
...
```
- 第一列：基因编号（从1开始）
- 第二列：聚类编号（从1开始）

## K-means算法伪代码

### 主要算法流程：

```
算法：K-means聚类
输入：数据集D，聚类数k，最大迭代次数max_iter
输出：聚类分配结果

1. 初始化阶段：
   如果提供质心文件：
       从文件读取k个初始质心
   否则：
       随机选择k个数据点作为初始质心

2. 迭代聚类过程：
   对于 iteration = 1 到 max_iter：
       a) 分配阶段：
          对于每个基因 gene_i：
              计算gene_i到每个质心的欧几里得距离
              将gene_i分配给距离最近的质心对应的聚类

       b) 检查收敛：
          如果所有基因的聚类分配都没有改变：
              输出"算法收敛"并退出循环

       c) 更新质心：
          对于每个聚类 cluster_j：
              计算该聚类中所有基因的平均表达值
              将平均值设为新的质心

   如果达到最大迭代次数仍未收敛：
       输出"达到最大迭代次数"

3. 输出结果：
   将每个基因的聚类分配写入kmeans.out文件
```

### 关键函数说明：

1. **欧几里得距离计算**：
```
函数 euclidean_distance(point1, point2):
    distance = 0
    对于 i = 0 到 维度数-1：
        distance += (point1[i] - point2[i])²
    返回 sqrt(distance)
```

2. **聚类分配**：
```
函数 assign_clusters():
    对于每个基因：
        min_distance = 无穷大
        closest_cluster = 0
        对于每个质心：
            distance = euclidean_distance(基因, 质心)
            如果 distance < min_distance：
                min_distance = distance
                closest_cluster = 当前质心索引
        将基因分配给closest_cluster
```

3. **质心更新**：
```
函数 update_centroids():
    对于每个聚类：
        找到属于该聚类的所有基因
        如果聚类不为空：
            计算所有基因在每个维度上的平均值
            更新质心为平均值向量
        否则：
            保持原质心不变
```

### 算法特点：

- **收敛检测**：算法会检查连续两次迭代之间聚类分配是否发生变化，如果没有变化则认为收敛
- **最大迭代限制**：防止算法无限运行，在达到最大迭代次数时强制停止
- **随机种子**：使用固定随机种子（42）确保结果的可重现性
- **空聚类处理**：如果某个聚类没有分配到任何基因，保持其质心不变

### 数据处理：

- **数据加载**：从制表符分隔文件中读取2467个基因的79维表达数据
- **数据验证**：检查数据完整性和质心文件格式的正确性
- **结果输出**：按照指定格式输出聚类结果，基因和聚类编号都从1开始

## 项目背景

本项目实现K-means聚类算法用于分析酵母基因表达数据。数据包含2467个基因在79种不同实验条件下的表达值，这些实验条件包括饥饿、糖供应变化、细胞分裂同步化等环境变化。通过聚类分析，可以识别在相似条件下表现出相似表达模式的基因群体，有助于理解基因功能和调控机制。

数据来源：Eisen, M.B., Spellman, P.T., Brown, P.O., Botstein, D. Cluster analysis and display of genome-wide expression patterns. PNAS. 95:14863-14868.

## 技术细节

### 距离度量方法
- **欧几里得距离**：distance(p1, p2) = sqrt(sum((p1[i] - p2[i])^2))

### 收敛判断标准
- 当所有数据点的聚类分配都不再发生变化时，算法收敛

### 算法复杂度
- **时间复杂度**：O(t × k × n × d)
  - t = 迭代次数
  - k = 聚类数量
  - n = 数据点数量
  - d = 数据维度
- **空间复杂度**：O(k × d + n)

### 实现特点
- 使用固定随机种子（42）确保随机初始化的可重现性
- 处理空聚类：保持之前的质心不变
- 提前停止：如果在最大迭代次数前达到收敛则提前结束
- 提供聚类统计摘要信息

### 文件格式详细说明

**输入格式**：
- 制表符分隔的数据文件，每行代表一个基因
- 每行包含所有实验条件下的表达值
- 可选质心文件：k行制表符分隔的质心坐标

**输出格式**：
- 文件名："kmeans.out"，包含两列：
  - 第1列：基因编号（从1开始）
  - 第2列：聚类分配（从1开始）

### 测试验证
- 使用真实酵母数据：2467个基因，79个实验条件
- 测试质心文件：data_proj_2/test_centroids.txt（k=3的初始质心）
- 已成功测试随机初始化和文件初始化两种方式
