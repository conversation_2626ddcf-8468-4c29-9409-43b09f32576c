## 四、模型建立与算法实现

为了解决城市交叉口信号灯配时的优化问题，本文选择经典的交通工程理论——韦伯斯特（Webster）信号配时法作为核心模型。该方法以最小化交叉口车辆平均延误为目标，在理论完备性和实践有效性上得到了广泛认可。本章将详细阐述模型的建立、参数设定及针对三个具体问题的算法实现流程。

### 4.1 模型基本原理与参数设定

#### 4.1.1 韦伯斯特模型
韦伯斯特模型通过计算最优信号周期时长和绿灯时间，来平衡不同方向交通流的需求，从而提升交叉口的通行效率。其核心思想是根据各进口道的交通流量比例来分配绿灯时间。

#### 4.1.2 关键参数设定
模型计算依赖于以下几个关键参数：

1.  **车流量 (q)**：指单位时间内通过某进口道的车辆数，单位为 `辆/小时` (veh/h)。该数据直接来源于题目表1。
2.  **饱和流率 (s)**：指在理想条件下（即绿灯持续，车辆不断），单个车道单位时间内能通过的最大车辆数。这是衡量车道通行能力的关键指标。根据《城市道路设计规范》及相关研究，城市主干道单车道饱和流率通常取值为 `1600-2000 veh/h/ln`。为使模型具有普遍性，本文取其经验中间值 **`s = 1800 veh/h/ln`**。我们假设每个进口方向均为单车道。
3.  **流量比 (y)**：定义为实际车流量与饱和流率之比，即 `y = q / s`。它反映了某交通流对绿灯时间的需求程度，是信号配时的核心计算因子。
4.  **损失时间 (L)**：指每个信号周期中未被有效利用的时间，主要包括车辆启动延误和交叉口清空时间。根据经验，每个相位（Phase）的损失时间通常设为 `5` 秒。对于一个典型的双相位十字路口，总损失时间 `L = 2 * 5 = 10` 秒。
5.  **黄灯时间**：根据交通法规，黄灯时间通常设置为 `3` 秒。

### 4.2 相位设计
相位（Phase）是指在同一时间内获得通行权（绿灯）的一组互不冲突的交通流的集合。对C1-C4这四个主要交叉口，我们根据路网图（图1）设计了如下的双相位方案，以简化模型并覆盖主要直行交通流：

*   **交叉口 C1**:
    *   相位一：东西向交通流（D5: C2→C1, D10: C5→C1）
    *   相位二：南北向交通流（D4: C3→C1, D12: C12→C1）
*   **交叉口 C2**:
    *   相位一：东西向交通流（D1: C1→C2, D16: C7→C2）
    *   相位二：南北向交通流（D6: C4→C2, D13: C6→C2）
*   **交叉口 C3**:
    *   相位一：东西向交通流（D7: C4→C3, D21: C10→C3）
    *   相位二：南北向交通流（D2: C1→C3, D20: C9→C3）
*   **交叉口 C4**:
    *   相位一：东西向交通流（D8: C3→C4, D18: C8→C4）
    *   相位二：南北向交通流（D3: C1→C4, D23: C11→C4）

### 4.3 核心算法流程
基于韦伯斯特模型，我们设计了如下计算信号配时的算法流程：

**步骤 1：计算各相位关键流量比**
对于每个相位 `i`，其包含一个或多个交通流。该相位的通行能力瓶颈取决于其中流量比 `y` 最大的那个交通流。因此，我们定义相位的**关键流量比 `y_crit_i`** 为该相位内所有交通流流量比的最大值。
$$ y_{crit,i} = \max(y_{i,1}, y_{i,2}, \dots, y_{i,n}) $$
其中 `y_i,n` 为相位 `i` 中第 `n` 条交通流的流量比。

**步骤 2：计算交叉口总关键流量比之和 (Y)**
将交叉口所有相位的关键流量比相加，得到总关键流量比 `Y`。该值是判断交叉口饱和程度的重要指标。如果 `Y ≥ 1`，则表示交叉口已处于过饱和状态，常规信号配时无法满足需求。
$$ Y = \sum_{i=1}^{N} y_{crit,i} $$
其中 `N` 为相位的总数。

**步骤 3：计算最优信号周期时长 (C_opt)**
韦伯斯特给出了计算最优周期时长的经典公式，该周期旨在最小化车辆总延误：
$$ C_{opt} = \frac{1.5L + 5}{1 - Y} $$
其中 `L` 为周期总损失时间 (`L = N * 5` 秒)。为便于实际应用，计算出的 `C_opt` 通常向上取整到最接近的5的倍数。

**步骤 4：分配有效绿灯时间**
一个信号周期内的有效绿灯总时间 `G_e` 为周期时长减去总损失时间：
$$ G_e = C_{opt} - L $$
`G_e` 将按照各相位的关键流量比 `y_crit_i` 的比例进行分配。相位 `i` 的有效绿灯时间 `g_e_i` 为：
$$ g_{e,i} = \frac{y_{crit,i}}{Y} \times G_e $$

**步骤 5：确定各相位实际显示灯时**
每个相位的实际显示绿灯时间 `g_i` 等于其有效绿灯时间 `g_e_i`。红灯时间 `r_i` 则为周期时长减去该相位的绿灯和黄灯时间。
*   绿灯时长: `Green_i = g_{e,i}`
*   黄灯时长: `Amber_i = 3` 秒
*   红灯时长: `Red_i = C_{opt} - Green_i - Amber_i`

### 4.4 具体问题求解实现

#### 4.4.1 问题（1）：固定周期下确保不拥堵的配时方案
**目标**：在固定的信号周期时长下，为C1-C4分配各方向的绿灯时间，以保证不产生大的拥堵。
**实现**：
1.  设定一个固定的、较长的信号周期，例如 **`C_fixed = 120` 秒**。这是一个城市主干道常见的周期长度，足以应对较高的交通流量。
2.  对每个交叉口（C1-C4），执行算法流程的**步骤1**和**步骤2**，计算出各相位的关键流量比 `y_crit_i` 和总关键流量比 `Y`。
3.  跳过**步骤3**（计算最优周期），直接使用给定的 `C_fixed`。
4.  执行**步骤4**和**步骤5**，计算有效绿灯总时间 `G_e = 120 - L`，并按比例 `y_crit_i / Y` 将 `G_e` 分配给各个相位，最终得到各方向的红绿灯时长。
此方案的核心是利用一个足够长的周期，确保即使在高峰期，`Y` 值小于1（不饱和），从而能够通过按流量比例分配时间来消散到达的车辆，避免永久性排队。

#### 4.4.2 问题（2）：优化配时实现整体效率最优
**目标**：通过优化C1-C4路口的信号灯时间分配，使得整体通行时间效率最优。
**实现**：
此问题是韦伯斯特模型的直接应用，旨在最小化车辆延误，从而达到效率最优。
1.  对每个交叉口（C1-C4），完整地执行**步骤1至步骤5**的全部算法流程。
2.  为每个交叉口独立计算其**最优信号周期时长 `C_opt`**。
3.  基于各自的 `C_opt`，为每个交叉口计算并分配最优的绿灯时间。
与问题（1）不同，此方案不采用统一的固定周期，而是为每个交叉口“量身定制”最适合其自身交通压力的信号周期，从而实现局部最优，并最终提升全局网络的运行效率。

#### 4.4.3 问题（3）：高考期间的动态调整方案
**目标**：在高考期间，针对性调整C1-C4的信号灯时间，以应对局部交通流量的增长，保障全局交通流量最优。
**实现**：
此问题是一个动态交通管理的实例，模型需要根据变化的输入数据进行重新优化。
1.  **数据更新**：首先识别受影响的道路。根据题意，“前往7号节点”和“从7号节点返回”的流量增加10%。查阅数据表可知，与C7节点直接相连的道路是 **D15 (C2→C7)** 和 **D16 (C7→C2)**。
2.  **流量调整**：在原始车流量数据的基础上，将D15和D16的车流量 `q` 增加10%，即 `q_new = q * 1.1`，生成一份“高考期间”的交通流新数据集。
3.  **重新优化**：使用这份新的数据集，对所有四个交叉口（C1-C4）**重复问题（2）的完整实现流程**。即，为每个交叉口重新计算关键流量比 `Y`、最优周期 `C_opt` 和绿灯分配方案。
    *   由于D16的车流量增加，交叉口C2的交通压力会显著上升，其 `Y` 值和最优周期 `C_opt` 预计会增大。包含D16的相位的绿灯时间也将相应增加。
    *   虽然其他交叉口的输入流量没有直接变化，但重新计算所有路口可以确保系统层面的协调与最优。

