==================================================
问题 (1): 固定周期时长下的信号配时方案
假设固定周期时长为 120 秒，这是城市干道常见的周期长度。
==================================================

--- 交叉口 C1 配时方案 ---
总关键流量比 Y = 0.806
信号周期时长 C = 120 秒
--------------------
相位: Phase 1 (C2-C5方向)
  绿灯时长: 53 秒
  黄灯时长: 3 秒
  红灯时长: 64 秒
相位: Phase 2 (C3-C12方向)
  绿灯时长: 57 秒
  黄灯时长: 3 秒
  红灯时长: 60 秒


--- 交叉口 C2 配时方案 ---
总关键流量比 Y = 0.833
信号周期时长 C = 120 秒
--------------------
相位: Phase 1 (C1-C7方向)
  绿灯时长: 59 秒
  黄灯时长: 3 秒
  红灯时长: 58 秒
相位: Phase 2 (C4-C6方向)
  绿灯时长: 51 秒
  黄灯时长: 3 秒
  红灯时长: 66 秒


--- 交叉口 C3 配时方案 ---
总关键流量比 Y = 0.722
信号周期时长 C = 120 秒
--------------------
相位: Phase 1 (C4-C10方向)
  绿灯时长: 59 秒
  黄灯时长: 3 秒
  红灯时长: 58 秒
相位: Phase 2 (C1-C9方向)
  绿灯时长: 51 秒
  黄灯时长: 3 秒
  红灯时长: 66 秒


--- 交叉口 C4 配时方案 ---
总关键流量比 Y = 0.694
信号周期时长 C = 120 秒
--------------------
相位: Phase 1 (C3-C8方向)
  绿灯时长: 57 秒
  黄灯时长: 3 秒
  红灯时长: 60 秒
相位: Phase 2 (C1-C11方向)
  绿灯时长: 53 秒
  黄灯时长: 3 秒
  红灯时长: 64 秒


==================================================
问题 (2): 优化信号周期与时间分配以达到最优效率
使用韦伯斯特公式计算各交叉口的最优周期，以最小化延误。
==================================================

--- 交叉口 C1 配时方案 ---
总关键流量比 Y = 0.806
信号周期时长 C = 105 秒
--------------------
相位: Phase 1 (C2-C5方向)
  绿灯时长: 46 秒
  黄灯时长: 3 秒
  红灯时长: 56 秒
相位: Phase 2 (C3-C12方向)
  绿灯时长: 49 秒
  黄灯时长: 3 秒
  红灯时长: 53 秒


--- 交叉口 C2 配时方案 ---
总关键流量比 Y = 0.833
信号周期时长 C = 120 秒
--------------------
相位: Phase 1 (C1-C7方向)
  绿灯时长: 59 秒
  黄灯时长: 3 秒
  红灯时长: 58 秒
相位: Phase 2 (C4-C6方向)
  绿灯时长: 51 秒
  黄灯时长: 3 秒
  红灯时长: 66 秒


--- 交叉口 C3 配时方案 ---
总关键流量比 Y = 0.722
信号周期时长 C = 70 秒
--------------------
相位: Phase 1 (C4-C10方向)
  绿灯时长: 32 秒
  黄灯时长: 3 秒
  红灯时长: 35 秒
相位: Phase 2 (C1-C9方向)
  绿灯时长: 28 秒
  黄灯时长: 3 秒
  红灯时长: 39 秒


--- 交叉口 C4 配时方案 ---
总关键流量比 Y = 0.694
信号周期时长 C = 65 秒
--------------------
相位: Phase 1 (C3-C8方向)
  绿灯时长: 29 秒
  黄灯时长: 3 秒
  红灯时长: 33 秒
相位: Phase 2 (C1-C11方向)
  绿灯时长: 26 秒
  黄灯时长: 3 秒
  红灯时长: 36 秒


==================================================
问题 (3): 高考期间的动态调整方案
前往C7和从C7返回的道路流量增加10%，重新优化配时以保障全局交通流量。
==================================================

受影响的道路: ['D15', 'D16']
/Users/<USER>/Desktop/Code-money2/4608182305978047212/main.py:210: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '715.0000000000001' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.
  df_gaokao.loc[df_gaokao['道路编号'] == road, '车流量(辆/h)'] = new_flow
道路 D15 的流量从 650 增加到 715
道路 D16 的流量从 650.0 增加到 715

--- 高考期间新配时方案 ---

--- 交叉口 C1 配时方案 ---
总关键流量比 Y = 0.806
信号周期时长 C = 105 秒
--------------------
相位: Phase 1 (C2-C5方向)
  绿灯时长: 46 秒
  黄灯时长: 3 秒
  红灯时长: 56 秒
相位: Phase 2 (C3-C12方向)
  绿灯时长: 49 秒
  黄灯时长: 3 秒
  红灯时长: 53 秒


--- 交叉口 C2 配时方案 ---
总关键流量比 Y = 0.833
信号周期时长 C = 120 秒
--------------------
相位: Phase 1 (C1-C7方向)
  绿灯时长: 59 秒
  黄灯时长: 3 秒
  红灯时长: 58 秒
相位: Phase 2 (C4-C6方向)
  绿灯时长: 51 秒
  黄灯时长: 3 秒
  红灯时长: 66 秒


--- 交叉口 C3 配时方案 ---
总关键流量比 Y = 0.722
信号周期时长 C = 70 秒
--------------------
相位: Phase 1 (C4-C10方向)
  绿灯时长: 32 秒
  黄灯时长: 3 秒
  红灯时长: 35 秒
相位: Phase 2 (C1-C9方向)
  绿灯时长: 28 秒
  黄灯时长: 3 秒
  红灯时长: 39 秒


--- 交叉口 C4 配时方案 ---
总关键流量比 Y = 0.694
信号周期时长 C = 65 秒
--------------------
相位: Phase 1 (C3-C8方向)
  绿灯时长: 29 秒
  黄灯时长: 3 秒
  红灯时长: 33 秒
相位: Phase 2 (C1-C11方向)
  绿灯时长: 26 秒
  黄灯时长: 3 秒
  红灯时长: 36 秒