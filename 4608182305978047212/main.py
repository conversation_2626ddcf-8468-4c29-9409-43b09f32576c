import pandas as pd
import io

# --- 数据准备 ---
# 将题目中的表格数据读入Pandas DataFrame
data = """
道路编号,起点,终点,距离(km),平均车速(km/h),车流量(辆/h)
D1,C1,C2,2,40,800
D2,C1,C3,2,40,600
D3,C1,C4,3,30,600
D4,C3,C1,3,30,600
D5,C2,C1,2,35,700
D6,C4,C2,2,35,700
D7,C4,C3,2,45,550
D8,C3,C4,2,45,550
D9,C1,C5,3,40,500
D10,C5,C1,3,30,500
D11,C1,C12,2,40,750
D12,C12,C1,2,40,750
D13,C6,C2,4,40,600
D14,C2,C6,4,40,600
D15,C2,C7,3,40,650
D16,C7,C2,3,40,650
D17,C4,C8,4,40,650
D18,C8,C4,4,40,650
D19,C4,C9,3,40,600
D20,C9,C3,3,40,600
D21,C3,C10,3,40,700
D22,C10,C3,3,40,700
D23,C11,C4,4,45,600
D24,C11,C3,4,45,600
"""

df = pd.read_csv(io.StringIO(data))

# --- 模型参数定义 ---
# 饱和流率 (辆/小时/车道)，假设每个方向为单车道
SATURATION_FLOW = 1800  
# 每个相位的损失时间 (秒)，包括启动和清场
LOST_TIME_PER_PHASE = 5  
# 黄灯时间 (秒)
AMBER_TIME = 3

# 定义C1-C4交叉口的相位
# 每个相位包含一组不冲突的交通流（用道路编号表示）
PHASE_DEFINITIONS = {
    'C1': {
        'Phase 1 (C2-C5方向)': ['D5', 'D10'],
        'Phase 2 (C3-C12方向)': ['D4', 'D12']
    },
    'C2': {
        'Phase 1 (C1-C7方向)': ['D1', 'D16'],
        'Phase 2 (C4-C6方向)': ['D6', 'D13']
    },
    'C3': {
        'Phase 1 (C4-C10方向)': ['D7', 'D21'],
        'Phase 2 (C1-C9方向)': ['D2', 'D20']
    },
    'C4': {
        'Phase 1 (C3-C8方向)': ['D8', 'D18'],
        'Phase 2 (C1-C11方向)': ['D3', 'D23']
    }
}


# ---核心计算函数---
def calculate_signal_timing(intersection_id, traffic_df, fixed_cycle_time=None):
    """
    使用韦伯斯特方法计算指定交叉口的信号配时
    :param intersection_id: 交叉口编号 (e.g., 'C1')
    :param traffic_df: 包含车流量数据的DataFrame
    :param fixed_cycle_time: 如果提供，则使用固定周期时长；否则，计算最优周期
    :return: 包含配时结果的字典
    """
    phases = PHASE_DEFINITIONS[intersection_id]
    num_phases = len(phases)
    total_lost_time = num_phases * LOST_TIME_PER_PHASE

    phase_results = {}
    total_critical_flow_ratio = 0

    # 1. 计算每个相位的关键流量比
    for phase_name, road_ids in phases.items():
        max_flow_ratio = 0
        critical_road_id = None
        for road_id in road_ids:
            # 查找对应道路的车流量
            flow = traffic_df.loc[traffic_df['道路编号'] == road_id, '车流量(辆/h)'].iloc[0]
            flow_ratio = flow / SATURATION_FLOW
            if flow_ratio > max_flow_ratio:
                max_flow_ratio = flow_ratio
                critical_road_id = road_id
        
        phase_results[phase_name] = {
            'critical_flow_ratio': max_flow_ratio,
            'critical_road': critical_road_id
        }
        total_critical_flow_ratio += max_flow_ratio

    Y = total_critical_flow_ratio
    
    # 2. 检查是否过饱和
    if Y >= 1:
        return {"error": f"交叉口 {intersection_id} 处于过饱和状态 (Y={Y:.2f} >= 1)，无法进行有效配时。"}

    # 3. 计算周期时长
    if fixed_cycle_time:
        cycle_time = fixed_cycle_time
    else:
        # Webster's optimal cycle time formula
        cycle_time = (1.5 * total_lost_time + 5) / (1 - Y)
        # 实际周期通常取整到5的倍数
        cycle_time = round(cycle_time / 5) * 5

    # 4. 分配绿灯时间
    effective_green_time = cycle_time - total_lost_time
    
    final_results = {
        'intersection_id': intersection_id,
        'total_critical_flow_ratio_Y': f"{Y:.3f}",
        'cycle_time_C (s)': int(cycle_time),
        'phases': {}
    }

    for phase_name, result in phase_results.items():
        y_i = result['critical_flow_ratio']
        # 分配有效绿灯时间
        green_time = (y_i / Y) * effective_green_time
        # 计算红灯时间
        red_time = cycle_time - green_time - AMBER_TIME
        
        # 将结果添加到最终输出
        final_results['phases'][phase_name] = {
            'green_time (s)': int(round(green_time)),
            'amber_time (s)': AMBER_TIME,
            'red_time (s)': int(round(red_time))
        }

    return final_results


def print_results(results):
    """格式化打印结果"""
    if "error" in results:
        print(results["error"])
        return
        
    print(f"--- 交叉口 {results['intersection_id']} 配时方案 ---")
    print(f"总关键流量比 Y = {results['total_critical_flow_ratio_Y']}")
    print(f"信号周期时长 C = {results['cycle_time_C (s)']} 秒")
    print("-" * 20)
    for phase_name, timing in results['phases'].items():
        print(f"相位: {phase_name}")
        print(f"  绿灯时长: {timing['green_time (s)']} 秒")
        print(f"  黄灯时长: {timing['amber_time (s)']} 秒")
        print(f"  红灯时长: {timing['red_time (s)']} 秒")
    print("\n")


# --- 问题求解 ---

# (1) 计算固定信号灯时长条件下的配时，确保不堵车
print("="*50)
print("问题 (1): 固定周期时长下的信号配时方案")
print("假设固定周期时长为 120 秒，这是城市干道常见的周期长度。")
print("="*50 + "\n")

FIXED_CYCLE = 120
for intersection in ['C1', 'C2', 'C3', 'C4']:
    result_q1 = calculate_signal_timing(intersection, df, fixed_cycle_time=FIXED_CYCLE)
    print_results(result_q1)


# (2) 通过优化信号灯时间分配，使得整体通行时间效率最优
print("="*50)
print("问题 (2): 优化信号周期与时间分配以达到最优效率")
print("使用韦伯斯特公式计算各交叉口的最优周期，以最小化延误。")
print("="*50 + "\n")

for intersection in ['C1', 'C2', 'C3', 'C4']:
    result_q2 = calculate_signal_timing(intersection, df)
    print_results(result_q2)


# (3) 高考期间的动态调整
print("="*50)
print("问题 (3): 高考期间的动态调整方案")
print("前往C7和从C7返回的道路流量增加10%，重新优化配时以保障全局交通流量。")
print("="*50 + "\n")

# 创建一份新的数据副本进行修改
df_gaokao = df.copy()

# 找到前往C7和从C7返回的道路
# 前往C7: 终点为 C7 -> D15(C2->C7)
# 从C7返回: 起点为 C7 -> D16(C7->C2)
# 题目中提到“前往7号节点附近考场的车辆”，可能影响C7周边的道路。
# “考试结束从7号节点返回的车流量也将增加10%”，说明是双向的。
# 根据表格，与C7直接相连的道路是D15和D16。
roads_to_c7 = df_gaokao[df_gaokao['终点'] == 'C7']['道路编号'].tolist()
roads_from_c7 = df_gaokao[df_gaokao['起点'] == 'C7']['道路编号'].tolist()
affected_roads = roads_to_c7 + roads_from_c7

print(f"受影响的道路: {affected_roads}")

# 更新流量
for road in affected_roads:
    original_flow = df_gaokao.loc[df_gaokao['道路编号'] == road, '车流量(辆/h)'].iloc[0] # type: ignore
    new_flow = original_flow * 1.1
    df_gaokao.loc[df_gaokao['道路编号'] == road, '车流量(辆/h)'] = new_flow
    print(f"道路 {road} 的流量从 {original_flow} 增加到 {int(new_flow)}")

print("\n--- 高考期间新配时方案 ---\n")

# 重新计算并优化C1-C4的信号灯
# D15和D16的流量变化会直接影响C2的配时。其他路口也一并重新优化以应对可能的连锁反应。
for intersection in ['C1', 'C2', 'C3', 'C4']:
    result_q3 = calculate_signal_timing(intersection, df_gaokao)
    print_results(result_q3)