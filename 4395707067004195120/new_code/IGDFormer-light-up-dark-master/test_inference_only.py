#!/usr/bin/env python3
"""
IGDFormer纯推理测试脚本 - 只需要低光照图像，不需要标签
"""

import torch
import torch.nn.functional as F
import cv2
import os
import glob
import numpy as np
from tqdm import tqdm
import argparse
from datetime import datetime

from train_utils import create_model, tensor2img

def load_image(image_path):
    """加载单张图像"""
    # 读取图像
    img = cv2.imread(image_path)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img = img.astype(np.float32) / 255.0
    
    # 转换为tensor
    img_tensor = torch.from_numpy(img).permute(2, 0, 1).unsqueeze(0)
    return img_tensor

def pad_test(lq, model, window_size=4):
    """带padding的测试函数"""
    h, w = lq.shape[2], lq.shape[3]
    h_ = ((h + window_size) // window_size) * window_size
    w_ = ((w + window_size) // window_size) * window_size
    
    padh = h_ - h if h % window_size != 0 else 0
    padw = w_ - w if w % window_size != 0 else 0
    
    lq_padded = F.pad(lq, (0, padw, 0, padh), 'reflect')
    
    with torch.no_grad():
        restored = model(lq_padded)
    
    restored = restored[:, :, :h, :w]
    return restored

def test_igdformer_inference():
    """IGDFormer纯推理测试"""
    
    parser = argparse.ArgumentParser(description="IGDFormer纯推理测试")
    parser.add_argument('--input_dir', default='../train_data/in/', type=str, help='输入图像目录')
    parser.add_argument('--weights_path', default='./save_weights/ORFormer--100.pth', type=str, help='模型权重路径')
    parser.add_argument('--output_dir', default='./inference_results', type=str, help='结果保存目录')
    parser.add_argument('--window_size', default=4, type=int, help='窗口大小')
    
    args = parser.parse_args()
    
    print("🚀 IGDFormer纯推理测试")
    print("="*50)
    
    # 检查输入目录
    if not os.path.exists(args.input_dir):
        print(f"❌ 输入目录不存在: {args.input_dir}")
        return
    
    # 检查权重文件
    if not os.path.exists(args.weights_path):
        print(f"❌ 权重文件不存在: {args.weights_path}")
        print("可用的权重文件:")
        weights_dir = os.path.dirname(args.weights_path)
        if os.path.exists(weights_dir):
            for f in os.listdir(weights_dir):
                if f.endswith('.pth'):
                    print(f"  - {os.path.join(weights_dir, f)}")
        return
    
    # 获取所有图像文件
    image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(args.input_dir, ext)))
        image_files.extend(glob.glob(os.path.join(args.input_dir, ext.upper())))
    
    image_files.sort()
    
    if len(image_files) == 0:
        print(f"❌ 在 {args.input_dir} 中没有找到图像文件")
        return
    
    print(f"📊 找到 {len(image_files)} 张输入图像")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 创建模型
    print("🤖 加载模型...")
    try:
        model = create_model(40, 1, [1, 2, 2], attn_type='Mixing_attention_new')
        
        # 加载权重
        checkpoint = torch.load(args.weights_path, map_location='cpu')
        if 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'], strict=True)
            epoch = checkpoint.get('epoch', 'unknown')
            print(f"✅ 加载权重成功 (epoch: {epoch})")
        else:
            model.load_state_dict(checkpoint, strict=True)
            print(f"✅ 加载权重成功")
            
        model.to(device)
        model.eval()
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(args.output_dir, f"IGDFormer_inference_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"📁 结果保存到: {output_dir}")
    
    # 开始推理
    print("🔍 开始推理...")
    
    with torch.no_grad():
        for idx, image_path in enumerate(tqdm(image_files, desc="推理进度")):
            try:
                # 加载图像
                input_tensor = load_image(image_path).to(device)
                
                # 推理
                output_tensor = pad_test(input_tensor, model, window_size=args.window_size)
                
                # 转换为图像
                output_img = tensor2img(output_tensor)
                
                # 保存结果
                input_name = os.path.splitext(os.path.basename(image_path))[0]
                output_path = os.path.join(output_dir, f"{input_name}_enhanced.png")
                cv2.imwrite(output_path, output_img)
                
            except Exception as e:
                print(f"❌ 处理 {os.path.basename(image_path)} 失败: {e}")
                continue
    
    print("\n" + "="*50)
    print("🎉 推理完成!")
    print(f"📊 处理图像数量: {len(image_files)}")
    print(f"📁 结果保存在: {output_dir}")
    print("="*50)

if __name__ == "__main__":
    test_igdformer_inference()
