# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: train_utils.py
# @Author: ---
# @Institution: --- CSU&BUCEA, ---, China
# @Homepage: ---@---https://github.com/YanJieWen
# @E-mail: ---@---<EMAIL>
# @Site: 
# @Time: 12月 18, 2023
# ---

import importlib
import math
import random
import os
import sys

import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import torch
from functools import partial
import torch.nn.functional as F
from typing import List
import numpy as np
import cv2
# import d2l.torch as d2l

# import datasets as ds
# from datasets.my_datasets import Dataset_PariedImage
# from datasets.smid_datasets import Dataset_SMIDImage

from models.image_restoration_model import Mixing_Augment
from models.RetinexFormer_arch import RetinexFormer
from models import metrics

# from models.original_retinex import RetinexFormer

# 获取所有数据集模块
dataset_filenames = [os.path.splitext(file)[0] for file in os.listdir('./datasets/') if file.endswith('_datasets.py')]
_dataset_modules = [
    importlib.import_module(f'datasets.{file_name}')
    for file_name in dataset_filenames
]


def warmup_lr_scheduler(optimizer, warmup_iters, warmup_factor):
    """创建学习率预热调度器"""

    def f(x):
        """根据step数返回一个学习率倍率因子"""
        if x >= warmup_iters:  # 当迭代数大于给定的warmup_iters时，倍率因子为1
            return 1
        alpha = float(x) / warmup_iters
        # 迭代过程中倍率因子从warmup_factor -> 1
        return warmup_factor * (1 - alpha) + alpha

    return torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda=f)


def create_train_val_loader(data_root: str = None, mean: list = None, std: list = None,
                            data_type: List[str] = ['Train', 'Test'], gt_size: int = 128,
                            batch_size: int = 8, read_type: str = "SID"):
    """
    创建训练和验证数据加载器

    参数:
        data_root: 数据根目录
        mean: 均值
        std: 标准差
        data_type: 数据类型列表 ['Train', 'Test']
        gt_size: 图像大小
        batch_size: 批大小
        read_type: 读取类型 (LOLV1, FiveK, LOLV2, SMID, SID)
    """
    print(f"创建数据加载器 - 类型: {read_type}")

    if read_type in ['LOLV1', 'FiveK', 'LOLV2', 'all']:
        for m in _dataset_modules:
            data_cont = getattr(m, 'Dataset_PariedImage', None)
            if data_cont is not None:
                break
        if data_cont is None:
            raise ValueError('Dataset_PariedImage is not found!')
        train_set = data_cont(data_root, mean, std, gt_size=gt_size, data_type=data_type[0], geometric_augs=True)
        val_set = data_cont(data_root, mean, std, gt_size=gt_size, data_type=data_type[1], geometric_augs=False)
        train_loader = DataLoader(train_set, batch_size=batch_size, shuffle=True, num_workers=0, sampler=None)
        val_loader = DataLoader(val_set, batch_size=1, shuffle=False, num_workers=0, sampler=None)

    elif read_type == 'SMID':
        for m in _dataset_modules:
            data_cont = getattr(m, 'Dataset_SMIDImage', None)
            if data_cont is not None:
                break
        if data_cont is None:
            raise ValueError('Dataset_SMIDImage is not found!')
        gt_root = os.path.join(data_root, 'SMID_Long_np/')
        lq_root = os.path.join(data_root, 'SMID_LQ_np/')
        train_set = data_cont(gt_root=gt_root, lq_root=lq_root, phase='train')
        val_set = data_cont(gt_root=gt_root, lq_root=lq_root, phase='val')
        train_loader = DataLoader(train_set, batch_size=batch_size, shuffle=True, num_workers=0, sampler=None)
        val_loader = DataLoader(val_set, batch_size=1, shuffle=True, num_workers=0, sampler=None)

    elif read_type == 'CUSTOM':
        # 使用自定义数据集加载器
        from datasets.custom_dataset import Dataset_CustomImage
        train_set = Dataset_CustomImage(data_root, mean, std, gt_size=gt_size, data_type=data_type[0], geometric_augs=True)
        val_set = Dataset_CustomImage(data_root, mean, std, gt_size=gt_size, data_type=data_type[1], geometric_augs=False)
        train_loader = DataLoader(train_set, batch_size=batch_size, shuffle=True, num_workers=0, sampler=None)
        val_loader = DataLoader(val_set, batch_size=1, shuffle=False, num_workers=0, sampler=None)

    elif read_type == 'SID':
        for m in _dataset_modules:
            data_cont = getattr(m, 'Dataset_SIDImage', None)
            if data_cont is not None:
                break
        if data_cont is None:
            raise ValueError('Dataset_SIDImage is not found!')

        # 添加详细的路径调试信息
        gt_root = os.path.join(data_root, 'long_sid2')
        lq_root = os.path.join(data_root, 'short_sid2')

        # 确保路径格式正确
        gt_root = gt_root.replace('\\', '/')
        lq_root = lq_root.replace('\\', '/')

        print(f"GT 根目录: {gt_root}")
        print(f"LQ 根目录: {lq_root}")

        # 检查目录是否存在
        if not os.path.exists(gt_root):
            print(f"错误: GT 目录不存在 - {gt_root}")
            # 尝试创建目录
            os.makedirs(gt_root, exist_ok=True)
            print(f"已创建 GT 目录: {gt_root}")
        else:
            print(f"GT 目录存在")

        if not os.path.exists(lq_root):
            print(f"错误: LQ 目录不存在 - {lq_root}")
            os.makedirs(lq_root, exist_ok=True)
            print(f"已创建 LQ 目录: {lq_root}")
        else:
            print(f"LQ 目录存在")

        # 检查目录内容
        if os.path.exists(gt_root):
            gt_contents = os.listdir(gt_root)
            print(f"GT 目录内容: {gt_contents[:5]}... 共 {len(gt_contents)} 项")
            if len(gt_contents) > 0:
                first_item = os.path.join(gt_root, gt_contents[0])
                item_type = "文件" if os.path.isfile(first_item) else "目录"
                print(f"第一项是 {item_type}: {first_item}")

        if os.path.exists(lq_root):
            lq_contents = os.listdir(lq_root)
            print(f"LQ 目录内容: {lq_contents[:5]}... 共 {len(lq_contents)} 项")
            if len(lq_contents) > 0:
                first_item = os.path.join(lq_root, lq_contents[0])
                item_type = "文件" if os.path.isfile(first_item) else "目录"
                print(f"第一项是 {item_type}: {first_item}")

        # 创建数据集
        try:
            train_set = data_cont(gt_root=gt_root, lq_root=lq_root, phase='train')
            val_set = data_cont(gt_root=gt_root, lq_root=lq_root, phase='val')

            # 添加数据集大小检查
            print(f"训练集大小: {len(train_set)}")
            print(f"验证集大小: {len(val_set)}")

            if len(train_set) == 0:
                print("警告: 训练集为空!")

            if len(val_set) == 0:
                print("警告: 验证集为空!")
                # 如果没有验证集，使用训练集的一部分
                print("将使用训练集的前20%作为验证集")
                from torch.utils.data import random_split
                total_size = len(train_set)
                val_size = int(0.2 * total_size)
                train_size = total_size - val_size
                train_set, val_set = random_split(train_set, [train_size, val_size])
                print(f"新训练集大小: {len(train_set)}, 新验证集大小: {len(val_set)}")

            train_loader = DataLoader(train_set, batch_size=batch_size, shuffle=True, num_workers=0, sampler=None)
            val_loader = DataLoader(val_set, batch_size=1, shuffle=False, num_workers=0, sampler=None)

        except Exception as e:
            print(f"创建数据集时出错: {e}")
            print(f"错误类型: {type(e).__name__}")
            print(f"详细信息: {str(e)}")
            # 返回空数据加载器避免崩溃
            train_loader = DataLoader([], batch_size=batch_size, shuffle=True)
            val_loader = DataLoader([], batch_size=1, shuffle=False)

    else:
        raise ValueError(f'未知的数据类型: {read_type}')

    # 添加最终检查
    print(f"训练数据加载器大小: {len(train_loader.dataset)}")
    print(f"验证数据加载器大小: {len(val_loader.dataset)}")

    return train_loader, val_loader


def create_model(n_feat, stage, num_blocks, attn_type='Restormer', if_pretrained=False, weights_root=None):
    """创建模型"""
    assert attn_type in ['IGAB', 'Restormer', 'Swin_Transformer', 'Mixing_attention', 'Mixing_attention_new'], \
        f'{attn_type} 不是支持的注意力类型!'

    print(f"创建模型 - 注意力类型: {attn_type}")
    model = RetinexFormer(n_feat=n_feat, stage=stage, num_blocks=num_blocks, att_type=attn_type)

    if if_pretrained and weights_root is not None:
        print(f"加载预训练权重: {weights_root}")
        model.load_state_dict(torch.load(weights_root, map_location='cpu')['model'], strict=True)

    return model


def train_one_epoch(model, optimizer, data_loader, device, epoch, accelerator, loss_fun, lr_scheduler, pre_feq,
                    warmup=False, mini_gt_size=256, gt_size=512):
    """训练一个epoch"""
    model.train()
    mixsing_agumentation = Mixing_Augment(mixup_beta=1.2, use_identity=True, device=accelerator.device)
    _lr_scheduler = None

    if epoch == 0 and warmup:
        warmup_factor = 1.0 / 1000
        warmup_iters = min(1000, len(data_loader) - 1)
        _lr_scheduler = warmup_lr_scheduler(optimizer, warmup_iters, warmup_factor)
        _lr_scheduler = accelerator.prepare(_lr_scheduler)

    mloss = torch.zeros(1).to(device)
    num_batches = len(data_loader)

    for i, datas in enumerate(data_loader):
        # 添加一个随机的crop
        lq = datas['lq'].to(device)
        gt = datas['gt'].to(device)

        if mini_gt_size < gt_size:
            x0 = int((gt_size - mini_gt_size) * random.random())
            y0 = int((gt_size - mini_gt_size) * random.random())
            x1 = x0 + mini_gt_size
            y1 = y0 + mini_gt_size
            lq = lq[:, :, x0:x1, y0:y1]
            gt = gt[:, :, x0:x1, y0:y1]

        gt, lq = mixsing_agumentation(gt, lq)
        preds = model(lq)
        loss = loss_fun(preds, gt)
        loss_value = loss.item()
        mloss = (mloss * i + loss_value) / (i + 1)  # 更新平均损失

        if (i + 1) % pre_feq == 0:
            print(
                f'Epoch {epoch} [{i + 1}/{num_batches}] - Loss: {mloss.item():.6f} - LR: {optimizer.param_groups[0]["lr"]:.8f}')

        if not math.isfinite(loss_value):  # 当计算的损失为无穷大时停止训练
            print(f"损失值异常: {loss_value}, 停止训练")
            sys.exit(1)

        optimizer.zero_grad()
        accelerator.backward(loss)
        torch.nn.utils.clip_grad_norm_(model.parameters(), 0.01)
        optimizer.step()

        if _lr_scheduler is not None:
            _lr_scheduler.step()
        else:
            lr_scheduler.step()

    now_lr = optimizer.param_groups[0]["lr"]
    return mloss, now_lr


def pad_test(lq, model, window_size):
    """填充测试"""
    mod_pad_h, mod_pad_w = 0, 0
    _, _, h, w = lq.size()

    if h % window_size != 0:
        mod_pad_h = window_size - h % window_size
    if w % window_size != 0:
        mod_pad_w = window_size - w % window_size

    img = F.pad(lq, (0, mod_pad_w, 0, mod_pad_h), mode='reflect')
    model.eval()

    with torch.no_grad():
        pred = model(img)

    output = pred
    _, _, h, w = output.size()
    return output[:, :, 0:h - mod_pad_h, 0:w - mod_pad_w]


def tensor2img(tensor, rgb2bgr=True, out_type=np.uint8, min_max=(0, 1)):
    """将张量转换为图像

    Args:
        tensor: [1,c,h,w]
        rgb2bgr: bool, 是否将RGB转换为BGR
        out_type: 输出类型, 如np.uint8
        min_max: 值范围 [min, max]

    Returns:
        [h,w,c] -> bgr -> numpy
    """
    _tensor = tensor.squeeze(0).float().detach().cpu().clamp_(*min_max)
    _tensor = (_tensor - min_max[0]) / (min_max[1] - min_max[0])
    img_np = _tensor.numpy()
    img_np = img_np.transpose(1, 2, 0)

    if rgb2bgr:
        img_np = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)

    if out_type == np.uint8:
        img_np = (img_np * 255.0).round()

    result = img_np.astype(out_type)
    return result


def evaluate(model, data_loader, window_size, acc=None):
    """评估模型性能"""
    test = partial(pad_test, window_size=window_size)
    metrictor = [0.0, 0.0, 0]  # PSNR总和, SSIM总和, 样本计数

    model.eval()
    with torch.no_grad():
        for idx, val_data in enumerate(data_loader):
            if idx % 10 == 0:
                print(f"处理验证集样本 {idx}/{len(data_loader)}")

            data_lq = val_data['lq']
            data_gt = val_data['gt']

            # 确保在正确的设备上
            data_lq = data_lq.to(next(model.parameters()).device)
            data_gt = data_gt.to(next(model.parameters()).device)

            pred = test(lq=data_lq, model=model)
            sr_img = tensor2img(pred)
            gt_img = tensor2img(data_gt)

            # 计算PSNR和SSIM
            psnr_metrics = metrics.calculate_psnr(sr_img, gt_img)
            ssim_metrics = metrics.calculate_ssim(sr_img, gt_img)

            metrictor[0] += psnr_metrics
            metrictor[1] += ssim_metrics
            metrictor[2] += 1

            if idx >= 1500:  # 限制验证样本数量
                break

    # 计算平均值
    avg_psnr = metrictor[0] / metrictor[2] if metrictor[2] > 0 else 0.0
    avg_ssim = metrictor[1] / metrictor[2] if metrictor[2] > 0 else 0.0

    return avg_psnr, avg_ssim