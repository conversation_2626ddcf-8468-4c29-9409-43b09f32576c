#!/usr/bin/env python3
"""
IGDFormer自定义数据集测试脚本
"""

import sys
import os
import torch
import torch.nn.functional as F
import cv2
import numpy as np
from tqdm import tqdm
import pandas as pd
import argparse
from datetime import datetime

from train_utils import create_train_val_loader, create_model, tensor2img
from models import metrics

def pad_test(lq, model, window_size=4):
    """带padding的测试函数"""
    h, w = lq.shape[2], lq.shape[3]
    h_ = ((h + window_size) // window_size) * window_size
    w_ = ((w + window_size) // window_size) * window_size
    
    padh = h_ - h if h % window_size != 0 else 0
    padw = w_ - w if w % window_size != 0 else 0
    
    lq_padded = F.pad(lq, (0, padw, 0, padh), 'reflect')
    
    with torch.no_grad():
        restored = model(lq_padded)
    
    restored = restored[:, :, :h, :w]
    return restored

def test_igdformer():
    """测试IGDFormer"""
    
    parser = argparse.ArgumentParser(description="IGDFormer测试")
    parser.add_argument('--data_root', default='../train_data/', type=str, help='数据根目录')
    parser.add_argument('--weights_path', default='./save_weights/ORFormer--100.pth', type=str, help='模型权重路径')
    parser.add_argument('--output_dir', default='./test_results', type=str, help='结果保存目录')
    parser.add_argument('--batch_size', default=1, type=int, help='批次大小')
    parser.add_argument('--window_size', default=4, type=int, help='窗口大小')
    parser.add_argument('--max_test_num', default=500, type=int, help='最大测试数量')
    
    args = parser.parse_args()
    
    # 检查权重文件
    if not os.path.exists(args.weights_path):
        print(f"❌ 权重文件不存在: {args.weights_path}")
        print("可用的权重文件:")
        weights_dir = os.path.dirname(args.weights_path)
        if os.path.exists(weights_dir):
            for f in os.listdir(weights_dir):
                if f.endswith('.pth'):
                    print(f"  - {os.path.join(weights_dir, f)}")
        return
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 创建数据加载器
    print("📊 加载数据集...")
    try:
        result = create_train_val_loader(
            args.data_root, 
            mean=None, 
            std=None, 
            batch_size=args.batch_size,
            read_type='CUSTOM'
        )
        train_loader, val_loader = result
        print(f"✅ 验证集大小: {len(val_loader.dataset)}")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 创建模型
    print("🤖 加载模型...")
    try:
        model = create_model(40, 1, [1, 2, 2], attn_type='Mixing_attention_new')
        
        # 加载权重
        checkpoint = torch.load(args.weights_path, map_location='cpu')
        if 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'], strict=True)
            epoch = checkpoint.get('epoch', 'unknown')
            print(f"✅ 加载权重成功 (epoch: {epoch})")
        else:
            model.load_state_dict(checkpoint, strict=True)
            print(f"✅ 加载权重成功")
            
        model.to(device)
        model.eval()
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_dir = os.path.join(args.output_dir, f"IGDFormer_test_{timestamp}")
    pred_dir = os.path.join(result_dir, 'pred')
    lq_dir = os.path.join(result_dir, 'input')
    gt_dir = os.path.join(result_dir, 'target')
    
    for dir_path in [pred_dir, lq_dir, gt_dir]:
        os.makedirs(dir_path, exist_ok=True)
    
    print(f"📁 结果保存到: {result_dir}")
    
    # 测试
    print("🔍 开始测试...")
    psnr_list = []
    ssim_list = []
    
    with torch.no_grad():
        for idx, data_batch in enumerate(tqdm(val_loader, desc="测试进度")):
            if idx >= args.max_test_num:
                break
                
            # 获取数据
            lq = data_batch['lq'].to(device)
            gt = data_batch['gt'].to(device)
            
            # 推理
            pred = pad_test(lq, model, window_size=args.window_size)
            
            # 转换为图像
            pred_img = tensor2img(pred)
            gt_img = tensor2img(gt)
            lq_img = tensor2img(lq)
            
            # 计算指标
            psnr_func = getattr(metrics, 'calculate_psnr')
            ssim_func = getattr(metrics, 'calculate_ssim')
            
            psnr_val = psnr_func(pred_img, gt_img)
            ssim_val = ssim_func(pred_img, gt_img)
            
            psnr_list.append(psnr_val)
            ssim_list.append(ssim_val)
            
            # 保存图像
            img_name = f"{idx:05d}.png"
            cv2.imwrite(os.path.join(pred_dir, img_name), pred_img)
            cv2.imwrite(os.path.join(lq_dir, img_name), lq_img)
            cv2.imwrite(os.path.join(gt_dir, img_name), gt_img)
            
            # 显示进度
            if (idx + 1) % 50 == 0:
                avg_psnr = np.mean(psnr_list)
                avg_ssim = np.mean(ssim_list)
                print(f"已处理 {idx+1} 张 | 平均PSNR: {avg_psnr:.4f} | 平均SSIM: {avg_ssim:.4f}")
    
    # 计算最终指标
    final_psnr = np.mean(psnr_list)
    final_ssim = np.mean(ssim_list)
    
    print("\n" + "="*60)
    print("🎉 测试完成!")
    print(f"📊 测试结果:")
    print(f"   - 测试图像数量: {len(psnr_list)}")
    print(f"   - 平均PSNR: {final_psnr:.4f} dB")
    print(f"   - 平均SSIM: {final_ssim:.4f}")
    print(f"   - PSNR标准差: {np.std(psnr_list):.4f}")
    print(f"   - SSIM标准差: {np.std(ssim_list):.4f}")
    print(f"📁 结果保存在: {result_dir}")
    print("="*60)
    
    # 保存指标到Excel
    metrics_data = {
        'Image_ID': [f"{i:05d}" for i in range(len(psnr_list))],
        'PSNR': psnr_list,
        'SSIM': ssim_list
    }
    
    df = pd.DataFrame(metrics_data)
    excel_path = os.path.join(result_dir, 'metrics.xlsx')
    df.to_excel(excel_path, index=False)
    
    # 保存统计信息
    summary = {
        'Total_Images': len(psnr_list),
        'Average_PSNR': final_psnr,
        'Average_SSIM': final_ssim,
        'PSNR_Std': np.std(psnr_list),
        'SSIM_Std': np.std(ssim_list),
        'Model_Path': args.weights_path,
        'Test_Time': timestamp
    }
    
    summary_df = pd.DataFrame([summary])
    summary_path = os.path.join(result_dir, 'summary.xlsx')
    summary_df.to_excel(summary_path, index=False)
    
    print(f"📈 详细指标保存到: {excel_path}")
    print(f"📋 测试摘要保存到: {summary_path}")

if __name__ == "__main__":
    test_igdformer()
