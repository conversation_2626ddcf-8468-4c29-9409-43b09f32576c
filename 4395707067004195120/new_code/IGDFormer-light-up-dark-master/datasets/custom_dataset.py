# -*- coding: utf-8 -*-
"""
自定义数据集加载器，适配您的训练数据格式
"""

import numpy as np
from torch.utils.data import Dataset
from PIL import Image
import torchvision.transforms.functional as F
import os
import cv2
import glob

from datasets.transforms import *

class Dataset_CustomImage(Dataset):
    """
    自定义数据集类，适配您的 train_data/in 和 train_data/label 格式
    """
    
    def __init__(self, data_root, mean=None, std=None, gt_size=128, data_type='Train', geometric_augs=True):
        super(Dataset_CustomImage, self).__init__()
        self.mean = mean if mean is not None else None
        self.std = std if std is not None else None
        self.geometric_augs = geometric_augs
        self.data_type = data_type
        self.gt_size = gt_size
        
        # 设置数据路径
        if data_type == 'Train':
            # 使用80%的数据作为训练集
            self.lq_folder = os.path.join(data_root, 'in')
            self.gt_folder = os.path.join(data_root, 'label')
        else:  # Test/Val
            # 使用20%的数据作为验证集
            self.lq_folder = os.path.join(data_root, 'in')
            self.gt_folder = os.path.join(data_root, 'label')
        
        # 获取所有图像文件
        self.lq_paths = self._get_image_paths(self.lq_folder)
        self.gt_paths = self._get_image_paths(self.gt_folder)
        
        # 确保输入和标签图像数量匹配
        self.lq_paths.sort()
        self.gt_paths.sort()
        
        # 根据文件名匹配输入和标签图像
        self.paths = self._match_pairs()
        
        # 根据数据类型分割数据集
        total_len = len(self.paths)
        if data_type == 'Train':
            # 使用前80%作为训练集
            self.paths = self.paths[:int(0.8 * total_len)]
        else:
            # 使用后20%作为验证集
            self.paths = self.paths[int(0.8 * total_len):]
        
        print(f"{data_type} 数据集包含 {len(self.paths)} 对图像")
    
    def _get_image_paths(self, folder):
        """获取文件夹中的所有图像路径"""
        extensions = ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff']
        paths = []
        for ext in extensions:
            paths.extend(glob.glob(os.path.join(folder, ext)))
            paths.extend(glob.glob(os.path.join(folder, ext.upper())))
        return paths
    
    def _match_pairs(self):
        """匹配输入和标签图像对"""
        pairs = []
        
        # 创建文件名到路径的映射
        lq_dict = {}
        for path in self.lq_paths:
            filename = os.path.splitext(os.path.basename(path))[0]
            lq_dict[filename] = path
        
        gt_dict = {}
        for path in self.gt_paths:
            filename = os.path.splitext(os.path.basename(path))[0]
            gt_dict[filename] = path
        
        # 匹配相同文件名的图像对
        for filename in lq_dict:
            if filename in gt_dict:
                pairs.append({
                    'lq_path': lq_dict[filename],
                    'gt_path': gt_dict[filename]
                })
        
        print(f"成功匹配 {len(pairs)} 对图像")
        return pairs
    
    def __getitem__(self, idx):
        index = idx % len(self.paths)
        gt_path = self.paths[index]['gt_path']
        lq_path = self.paths[index]['lq_path']
        
        try:
            # 读取图像
            img_gt = cv2.cvtColor(cv2.imread(gt_path), cv2.COLOR_BGR2RGB).astype(np.float32) / 255.
            img_lq = cv2.cvtColor(cv2.imread(lq_path), cv2.COLOR_BGR2RGB).astype(np.float32) / 255.
            
            # 训练时进行数据增强
            if self.data_type == 'Train':
                img_gt, img_lq = padding(img_gt, img_lq, self.gt_size)
                img_gt, img_lq = paired_random_crop(img_gt, img_lq, self.gt_size, 1, gt_path)
                if self.geometric_augs:
                    img_gt, img_lq = random_augmentation(img_gt, img_lq)
            
            # 转换为张量
            img_gt, img_lq = F.to_tensor(img_gt), F.to_tensor(img_lq)
            
            # 标准化
            if self.mean is not None and self.std is not None:
                img_gt = normalize(img_gt, self.mean, self.std)
                img_lq = normalize(img_lq, self.mean, self.std)
            
            return {
                'lq': img_lq,
                'gt': img_gt,
                'lq_path': lq_path,
                'gt_path': gt_path
            }
            
        except Exception as e:
            print(f"读取图像失败: {lq_path}, {gt_path}, 错误: {e}")
            # 返回第一个有效的图像对
            return self.__getitem__(0)
    
    def __len__(self):
        return len(self.paths)
