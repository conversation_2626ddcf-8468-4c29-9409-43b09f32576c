import os.path as osp

import PIL.Image
import torch
import torch.utils.data as data
import datasets.utils as util
import torch.nn.functional as F
import random
import cv2
import numpy as np
import glob
import os
import functools

class Dataset_SIDImage(data.Dataset):
    def __init__(self, gt_root, lq_root, phase='train', train_size=[960,512]):
        self.cache_data = True
        self.half_N_frames = 5 // 2
        self.GT_root, self.LQ_root = gt_root, lq_root
        self.data_info = {'path_LQ': [], 'path_GT': [],
                          'folder': [], 'idx': [], 'border': []}
        self.imgs_LQ, self.imgs_GT = {}, {}
        self.phase = phase
        self.train_size = train_size
        
        # 修改1：检查是文件还是目录
        if os.path.isfile(self.LQ_root) and os.path.isfile(self.GT_root):
            # 如果是文件列表
            subfolders_LQ_origin = [self.LQ_root]
            subfolders_GT_origin = [self.GT_root]
        else:
            # 如果是目录
            subfolders_LQ_origin = util.glob_file_list(self.LQ_root)
            subfolders_GT_origin = util.glob_file_list(self.GT_root)
        
        # 确保数据配对
        assert len(subfolders_LQ_origin) == len(subfolders_GT_origin), 'not paired data'
        
        # 修改2：直接使用文件列表而不是目录
        if all(os.path.isfile(f) for f in subfolders_LQ_origin):
            # 如果所有条目都是文件
            img_paths_LQ = subfolders_LQ_origin
            img_paths_GT = subfolders_GT_origin
            max_idx = len(img_paths_LQ)
            
            # 添加调试信息
            print(f"找到 {max_idx} 个{self.phase}图像")
            
            # 创建虚拟文件夹名
            folder_name = "root"
            
            self.data_info['path_LQ'].extend(img_paths_LQ)
            self.data_info['path_GT'].extend(img_paths_GT)
            self.data_info['folder'].extend([folder_name] * max_idx)
            
            for i in range(max_idx):
                self.data_info['idx'].append('{}/{}'.format(i, max_idx))
            
            # 边界处理
            border_l = [0] * max_idx
            if max_idx > 0:
                # 处理前半部分
                start = min(self.half_N_frames, max_idx)
                for i in range(start):
                    border_l[i] = 1
                
                # 处理后半部分
                end = max(0, max_idx - self.half_N_frames)
                for i in range(end, max_idx):
                    border_l[i] = 1
            
            self.data_info['border'].extend(border_l)
            
            if self.cache_data:
                self.imgs_LQ[folder_name] = img_paths_LQ
                self.imgs_GT[folder_name] = img_paths_GT
        else:
            # 原始处理目录的代码
            subfolders_LQ = []
            subfolders_GT = []
            if self.phase == 'train':
                for mm in range(len(subfolders_LQ_origin)):
                    name = os.path.basename(subfolders_LQ_origin[mm])
                    if '0' in name[0] or '2' in name[0]:
                        subfolders_LQ.append(subfolders_LQ_origin[mm])
                        subfolders_GT.append(subfolders_GT_origin[mm])
            else:
                for mm in range(len(subfolders_LQ_origin)):
                    name = os.path.basename(subfolders_LQ_origin[mm])
                    if '1' in name[0]:
                        subfolders_LQ.append(subfolders_LQ_origin[mm])
                        subfolders_GT.append(subfolders_GT_origin[mm])
            
            # 添加调试信息
            print(f"找到 {len(subfolders_LQ)} 个{self.phase}子目录")
            
            for subfolder_LQ, subfolder_GT in zip(subfolders_LQ, subfolders_GT):
                subfolder_name = osp.basename(subfolder_LQ)
                img_paths_LQ = util.glob_file_list(subfolder_LQ)
                img_paths_GT = util.glob_file_list(subfolder_GT)
                max_idx = len(img_paths_LQ)
                
                # 添加调试信息
                print(f"子目录 '{subfolder_name}' 包含 {max_idx} 个图像")
                
                self.data_info['path_LQ'].extend(img_paths_LQ)
                self.data_info['path_GT'].extend(img_paths_GT)
                self.data_info['folder'].extend([subfolder_name] * max_idx)
                
                for i in range(max_idx):
                    self.data_info['idx'].append('{}/{}'.format(i, max_idx))
                
                # 边界处理
                border_l = [0] * max_idx
                if max_idx > 0:
                    # 处理前半部分
                    start = min(self.half_N_frames, max_idx)
                    for i in range(start):
                        border_l[i] = 1
                    
                    # 处理后半部分
                    end = max(0, max_idx - self.half_N_frames)
                    for i in range(end, max_idx):
                        border_l[i] = 1
                
                self.data_info['border'].extend(border_l)
                
                if self.cache_data:
                    self.imgs_LQ[subfolder_name] = img_paths_LQ
                    self.imgs_GT[subfolder_name] = img_paths_GT