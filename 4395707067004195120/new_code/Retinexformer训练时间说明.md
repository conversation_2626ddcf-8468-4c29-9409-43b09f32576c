# 📊 Retinexformer训练时间说明

## 🔢 **训练轮数配置**

### **原始配置 (RetinexFormer_Custom.yml)**
- **总迭代次数**: 75,000 iterations (已优化)
- **原始设置**: 150,000 iterations (太长，已减半)

### **迭代次数 vs Epoch换算**

根据您的数据集大小计算：
- **数据集大小**: ~1500张图像
- **批次大小**: 4张/batch
- **每个epoch**: 1500 ÷ 4 = 375 iterations

**换算结果**:
- 75,000 iterations ÷ 375 = **约200个epoch**
- 150,000 iterations ÷ 375 = **约400个epoch**

## ⏱️ **训练时间估算**

### **硬件配置影响**
- **RTX 4090**: ~0.5秒/iteration → 约10.5小时
- **RTX 3080**: ~0.8秒/iteration → 约16.7小时  
- **RTX 2080**: ~1.2秒/iteration → 约25小时
- **GTX 1080**: ~2.0秒/iteration → 约41.7小时

### **训练阶段划分**
1. **第一阶段**: 23,000 iterations (约61个epoch)
   - 固定学习率: 3e-4
   - 预计用时: 30-40% 总时间

2. **第二阶段**: 52,000 iterations (约139个epoch)
   - 余弦退火: 3e-4 → 1e-6
   - 预计用时: 60-70% 总时间

## 🎛️ **不同训练长度选项**

### **快速测试 (推荐新手)**
```yaml
total_iter: 30000  # 约80个epoch, 4-8小时
periods: [10000, 20000]
```

### **标准训练 (当前设置)**
```yaml
total_iter: 75000  # 约200个epoch, 10-25小时
periods: [23000, 52000]
```

### **完整训练 (最佳效果)**
```yaml
total_iter: 150000  # 约400个epoch, 20-50小时
periods: [46000, 104000]
```

## 📈 **训练监控**

### **验证频率**
- `val_freq: 2000` - 每2000次迭代验证一次
- 约每5.3个epoch验证一次

### **模型保存频率**
- `save_checkpoint_freq: 2000` - 每2000次迭代保存一次
- 约每5.3个epoch保存一次

### **日志显示频率**
- `print_freq: 500` - 每500次迭代显示一次
- 约每1.3个epoch显示一次

## 🚀 **推荐训练策略**

### **1. 快速验证 (第一次运行)**
```bash
# 修改配置为30k iterations
total_iter: 30000
periods: [10000, 20000]

# 运行训练
python3 basicsr/train.py --opt Options/RetinexFormer_Custom.yml
```

### **2. 标准训练 (当前配置)**
```bash
# 使用当前75k iterations配置
python3 basicsr/train.py --opt Options/RetinexFormer_Custom.yml
```

### **3. 完整训练 (最佳效果)**
```bash
# 修改回150k iterations
total_iter: 150000
periods: [46000, 104000]

# 运行训练
python3 basicsr/train.py --opt Options/RetinexFormer_Custom.yml
```

## 💡 **训练技巧**

### **断点续训**
如果训练中断，可以从最后的checkpoint继续：
```yaml
path:
  resume_state: experiments/Enhancement_RetinexFormer_Custom/training_states/75000.state
```

### **预训练模型**
可以使用官方预训练模型作为起点：
```yaml
path:
  pretrain_network_g: pretrained_weights/LOL_v1.pth
```

### **多GPU训练**
```bash
# 使用多GPU加速
bash train_multigpu.sh Options/RetinexFormer_Custom.yml 0,1 4321
```

## 📊 **训练完成标志**

训练完成后会在以下位置找到：
- **最终模型**: `experiments/Enhancement_RetinexFormer_Custom/models/net_g_75000.pth`
- **训练日志**: `experiments/Enhancement_RetinexFormer_Custom/train_Enhancement_RetinexFormer_Custom.log`
- **TensorBoard日志**: `tb_logger/Enhancement_RetinexFormer_Custom/`

---

**总结**: 当前配置为75,000次迭代(约200个epoch)，预计训练时间10-25小时，适合大多数情况。如需快速测试可改为30,000次迭代。
