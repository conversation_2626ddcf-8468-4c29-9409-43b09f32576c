#!/usr/bin/env python3
"""
为Retinexformer准备数据集
将train_data分割为训练集和验证集
"""

import os
import shutil
import glob
import random
from pathlib import Path

def prepare_retinex_data():
    """准备Retinexformer数据集"""
    
    # 源数据路径
    source_in = "train_data/in"
    source_label = "train_data/label"
    
    # 目标数据路径
    target_base = "data/Custom"
    train_in = os.path.join(target_base, "Train/input")
    train_gt = os.path.join(target_base, "Train/target")
    test_in = os.path.join(target_base, "Test/input")
    test_gt = os.path.join(target_base, "Test/target")
    
    # 创建目标目录
    for path in [train_in, train_gt, test_in, test_gt]:
        os.makedirs(path, exist_ok=True)
        print(f"创建目录: {path}")
    
    # 获取所有图像文件
    in_files = []
    label_files = []
    
    for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff']:
        in_files.extend(glob.glob(os.path.join(source_in, ext)))
        in_files.extend(glob.glob(os.path.join(source_in, ext.upper())))
        label_files.extend(glob.glob(os.path.join(source_label, ext)))
        label_files.extend(glob.glob(os.path.join(source_label, ext.upper())))
    
    # 按文件名排序
    in_files.sort()
    label_files.sort()
    
    print(f"找到 {len(in_files)} 个输入文件")
    print(f"找到 {len(label_files)} 个标签文件")
    
    # 匹配输入和标签文件
    pairs = []
    in_dict = {}
    for path in in_files:
        filename = os.path.splitext(os.path.basename(path))[0]
        in_dict[filename] = path
    
    label_dict = {}
    for path in label_files:
        filename = os.path.splitext(os.path.basename(path))[0]
        label_dict[filename] = path
    
    # 创建匹配的文件对
    for filename in in_dict:
        if filename in label_dict:
            pairs.append((in_dict[filename], label_dict[filename]))
    
    print(f"成功匹配 {len(pairs)} 对图像")
    
    # 随机打乱
    random.seed(42)
    random.shuffle(pairs)
    
    # 分割数据集 (80% 训练, 20% 验证)
    split_idx = int(0.8 * len(pairs))
    train_pairs = pairs[:split_idx]
    test_pairs = pairs[split_idx:]
    
    print(f"训练集: {len(train_pairs)} 对")
    print(f"验证集: {len(test_pairs)} 对")
    
    # 复制训练集文件
    for i, (in_path, label_path) in enumerate(train_pairs):
        # 获取文件扩展名
        in_ext = os.path.splitext(in_path)[1]
        label_ext = os.path.splitext(label_path)[1]
        
        # 使用统一的命名格式
        new_name = f"{i+1:04d}"
        
        # 复制文件
        shutil.copy2(in_path, os.path.join(train_in, new_name + in_ext))
        shutil.copy2(label_path, os.path.join(train_gt, new_name + label_ext))
        
        if (i + 1) % 100 == 0:
            print(f"已处理训练集 {i+1}/{len(train_pairs)} 对")
    
    # 复制验证集文件
    for i, (in_path, label_path) in enumerate(test_pairs):
        # 获取文件扩展名
        in_ext = os.path.splitext(in_path)[1]
        label_ext = os.path.splitext(label_path)[1]
        
        # 使用统一的命名格式
        new_name = f"{i+1:04d}"
        
        # 复制文件
        shutil.copy2(in_path, os.path.join(test_in, new_name + in_ext))
        shutil.copy2(label_path, os.path.join(test_gt, new_name + label_ext))
        
        if (i + 1) % 50 == 0:
            print(f"已处理验证集 {i+1}/{len(test_pairs)} 对")
    
    print("数据准备完成!")
    print(f"训练集位置: {train_in} 和 {train_gt}")
    print(f"验证集位置: {test_in} 和 {test_gt}")
    
    # 更新配置文件中的路径
    config_content = f"""# general settings
name: Enhancement_RetinexFormer_Custom
model_type: ImageCleanModel
scale: 1
num_gpu: 1  # set num_gpu: 0 for cpu mode
manual_seed: 100

# dataset and data loader settings
datasets:
  train:
    name: TrainSet
    type: Dataset_PairedImage
    dataroot_gt: {train_gt}
    dataroot_lq: {train_in}
    geometric_augs: true

    filename_tmpl: '{{}}'
    io_backend:
      type: disk

    # data loader
    use_shuffle: true
    num_worker_per_gpu: 4
    batch_size_per_gpu: 4

    ### ------- Training on single fixed-patch size 128x128---------
    mini_batch_sizes: [4]   
    iters: [150000]
    gt_size: 128   
    gt_sizes: [128]
    ### ------------------------------------------------------------

    dataset_enlarge_ratio: 1
    prefetch_mode: ~

  val:
    name: ValSet
    type: Dataset_PairedImage
    dataroot_gt: {test_gt}
    dataroot_lq: {test_in}
    io_backend:
      type: disk

# network structures
network_g:
  type: RetinexFormer
  in_channels: 3
  out_channels: 3
  n_feat: 40
  stage: 1
  num_blocks: [1,2,2]

# path
path:
  pretrain_network_g: ~
  strict_load_g: true
  resume_state: ~

# training settings
train:
  total_iter: 150000
  warmup_iter: -1 # no warm up
  use_grad_clip: true

  scheduler:
    type: CosineAnnealingRestartCyclicLR
    periods: [46000, 104000]       
    restart_weights: [1,1]
    eta_mins: [0.0003,0.000001]   
  
  mixing_augs:
    mixup: true
    mixup_beta: 1.2
    use_identity: true

  optim_g:
    type: Adam
    lr: !!float 2e-4
    betas: [0.9, 0.999]
  
  # losses
  pixel_opt:
    type: L1Loss
    loss_weight: 1
    reduction: mean

# validation settings
val:
  window_size: 4
  val_freq: !!float 2e3
  save_img: false
  rgb2bgr: true
  use_image: false
  max_minibatch: 8

  metrics:
    psnr:
      type: calculate_psnr
      crop_border: 0
      test_y_channel: false

# logging settings
logger:
  print_freq: 500
  save_checkpoint_freq: !!float 2e3
  use_tb_logger: true
  wandb:
    project: low_light_custom
    resume_id: ~

# dist training settings
dist_params:
  backend: nccl
  port: 29500"""

    # 保存更新的配置文件
    config_path = "Retinexformer-master/Options/RetinexFormer_Custom_Updated.yml"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"已生成更新的配置文件: {config_path}")

if __name__ == "__main__":
    prepare_retinex_data()
