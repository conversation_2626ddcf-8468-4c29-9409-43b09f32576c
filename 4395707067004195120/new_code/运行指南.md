# 🚀 三个低光照图像增强实验运行指南

## 📊 您的数据集信息
- **数据位置**: `train_data/in/` (低光照图像) 和 `train_data/label/` (正常光照图像)
- **数据量**: 约1500+对图像
- **格式**: 支持 PNG, JPG, JPEG, BMP, TIFF 等格式

---

## 🔬 **实验1: Zero-DCE (推荐先试)**

### 特点
- ✅ **无监督学习** - 只需要低光照图像，不需要标签
- ✅ **环境简单** - 依赖最少
- ✅ **训练快速** - 模型轻量

### 环境安装
```bash
# 创建环境
conda create -n zerodce python=3.7
conda activate zerodce

# 安装依赖
conda install pytorch=1.0.0 torchvision=0.2.1 cudatoolkit=10.0 -c pytorch
pip install opencv-python
```

### 运行步骤
```bash
cd 4395707067004195120/new_code/Zero-DCE-master/Zero-DCE_code

# 先测试训练显示功能 (可选，验证配置是否正确)
python test_training_display.py

# 正式训练 (已配置好数据路径和详细显示)
python lowlight_train.py

# 准备测试数据
python prepare_test_data.py

# 测试 (需要先训练完成)
python test_zerodce.py

# 或使用原始测试脚本
python lowlight_test.py
```

### 新增训练显示功能 ✨
- ✅ **详细的epoch进度显示** - 显示当前epoch和总epoch数
- ✅ **实时batch进度** - 显示当前batch、总batch数和完成百分比
- ✅ **详细的损失分解** - 分别显示TV、Spatial、Color、Exposure损失
- ✅ **时间统计** - 显示每个epoch用时和总训练时间
- ✅ **训练配置信息** - 开始时显示所有重要参数
- ✅ **模型保存提示** - 清楚显示模型保存位置

### 训练输出示例
```
🚀 开始训练 Zero-DCE
📊 训练配置:
   - 总epoch数: 100
   - 每epoch批次数: 187
   - 批次大小: 8
   - 学习率: 0.0001
   - 数据路径: ../../../train_data/in/
   - 模型保存路径: snapshots/
============================================================

📈 Epoch [1/100] 开始训练...
   [14:30:15] Batch [   5/187] ( 2.7%) | Loss: 0.234567 | TV: 0.1234 | Spa: 0.0567 | Col: 0.0234 | Exp: 0.0311
   [14:30:18] Batch [  10/187] ( 5.3%) | Loss: 0.223456 | TV: 0.1123 | Spa: 0.0534 | Col: 0.0223 | Exp: 0.0298
   ...

✅ Epoch [1/100] 完成!
   ⏱️  用时: 45.2秒 (0.8分钟)
   📊 平均损失: 0.198765
      - TV Loss: 0.098765
      - Spatial Loss: 0.045678
      - Color Loss: 0.023456
      - Exposure Loss: 0.030866
   💾 模型已保存: snapshots/Epoch_001.pth
------------------------------------------------------------
```

### 配置说明
- ✅ 已自动配置数据路径指向您的 `train_data/in/`
- ✅ 支持多种图像格式
- ✅ 自动显示找到的图像数量
- ✅ 优化了训练参数 (100 epochs, 每5个batch显示一次进度)
- ✅ **自动测试数据准备** - 从训练数据中分离200张图像作为测试集
- ✅ **完整测试流程** - 提供自动化测试脚本和结果保存

---

## 🔬 **实验2: IGDFormer**

### 特点
- ✅ **监督学习** - 使用输入-标签图像对
- ✅ **Transformer架构** - 先进的注意力机制
- ✅ **多GPU支持** - 可使用accelerate加速

### 环境安装
```bash
# 创建环境
conda create -n igdformer python=3.7
conda activate igdformer

# 安装PyTorch
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# 安装其他依赖
pip install accelerate matplotlib scikit-learn scikit-image opencv-python
pip install numpy tqdm tensorboard einops
```

### 运行步骤
```bash
cd 4395707067004195120/new_code/IGDFormer-light-up-dark-master

# 单GPU训练 (已配置好数据路径和自定义数据集)
python train_engine.py --batch_size 4

# 自定义验证频率 (默认每20个epoch验证一次)
python train_engine.py --batch_size 4 --val_freq 20

# 更频繁验证 (每10个epoch验证一次)
python train_engine.py --batch_size 4 --val_freq 10

# 多GPU训练
accelerate launch --multi_gpu train_engine.py --batch_size 8 --val_freq 20

# 验证
python validation.py
```

### 配置说明
- ✅ 已创建自定义数据集加载器 `datasets/custom_dataset.py`
- ✅ 自动匹配输入和标签图像对
- ✅ 自动分割80%训练集，20%验证集
- ✅ 支持数据增强和多种图像格式
- ✅ **优化验证频率** - 默认每20个epoch验证一次，大幅提升训练速度
- ✅ **智能模型保存** - 只在验证时保存最佳模型，定期保存checkpoint
- ✅ **修复epoch计数问题** - 现在会按照您设置的epoch数准确停止训练

### 重要修复 🔧
- **Epoch计数修复**: 原来代码会根据300000次迭代计算epoch数(约800个)，现在使用您设置的epoch数
- **学习率调度优化**: 自动调整学习率周期以适应实际训练长度
- **训练时间可控**: 设置100个epoch就真的只训练100个epoch

### 验证频率优化 ✨
- **默认设置**: 每20个epoch验证一次 (而不是每个epoch)
- **训练速度提升**: 减少约95%的验证时间
- **特殊验证**: 第1个epoch、最后1个epoch和每20个epoch都会验证
- **输出区分**: 训练epoch显示`[训练]`，验证epoch显示`[验证]`
- **模型保存**: 只在验证时保存最佳模型，每40个epoch保存checkpoint

---

## 🔬 **实验3: Retinexformer (最复杂但功能最全)**

### 特点
- ✅ **监督学习** - 基于Retinex理论
- ✅ **单阶段方法** - 端到端训练
- ✅ **完整框架** - 基于BasicSR，功能丰富

### 环境安装

**推荐方案 (PyTorch 2.0)**:
```bash
conda create -n retinex python=3.9 -y
conda activate retinex

conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
pip install matplotlib scikit-learn scikit-image opencv-python yacs joblib natsort h5py tqdm tensorboard
pip install einops gdown addict future lmdb numpy pyyaml requests scipy yapf lpips thop timm

cd 4395707067004195120/new_code/Retinexformer-master
python setup.py develop --no_cuda_ext
```

### 数据准备
```bash
cd 4395707067004195120/new_code

# 运行数据准备脚本 (将您的数据分割并重新组织)
python prepare_retinex_data.py
```

### 运行步骤
```bash
cd 4395707067004195120/new_code/Retinexformer-master

# 训练 (使用自定义配置)
python3 basicsr/train.py --opt Options/RetinexFormer_Custom_Updated.yml

# 多GPU训练
bash train_multigpu.sh Options/RetinexFormer_Custom_Updated.yml 0,1 4321

# 测试
python3 Enhancement/test_from_dataset.py --opt Options/RetinexFormer_Custom_Updated.yml --weights experiments/Enhancement_RetinexFormer_Custom/models/net_g_latest.pth
```

### 配置说明
- ✅ 已创建自定义配置文件 `Options/RetinexFormer_Custom.yml`
- ✅ 数据准备脚本会自动分割和重新组织您的数据
- ✅ 支持完整的训练/验证流程

---

## 📈 **推荐运行顺序**

### 1️⃣ **先试 Zero-DCE** (最简单)
- 环境配置最简单
- 训练速度最快
- 可以快速验证数据是否正确

### 2️⃣ **再试 IGDFormer** (中等难度)
- 有监督学习，效果可能更好
- 支持多GPU加速
- 代码结构清晰

### 3️⃣ **最后试 Retinexformer** (最复杂)
- 功能最完整
- 基于成熟的BasicSR框架
- 可能获得最好的效果

---

## 🔧 **常见问题解决**

### 1. CUDA版本不匹配
```bash
# 检查CUDA版本
nvidia-smi

# 根据CUDA版本安装对应的PyTorch
# CUDA 11.8: pytorch-cuda=11.8
# CUDA 11.3: cudatoolkit=11.3
```

### 2. 内存不足
```bash
# 减小batch_size
python train_engine.py --batch_size 2

# 或使用梯度累积
python train_engine.py --batch_size 1 --gradient_accumulation_steps 4
```

### 3. 数据路径问题
- 确保 `train_data/in/` 和 `train_data/label/` 存在
- 确保图像文件名匹配 (相同的基础文件名)

### 4. 权限问题
```bash
# 给脚本执行权限
chmod +x prepare_retinex_data.py
```

---

## 📊 **训练监控**

### TensorBoard监控
```bash
# 启动TensorBoard
tensorboard --logdir experiments/ --port 6006

# 在浏览器中访问
http://localhost:6006
```

### 训练日志
- Zero-DCE: 控制台输出
- IGDFormer: `logs/` 目录
- Retinexformer: `experiments/` 目录

---

## 🎯 **预期结果**

- **训练时间**: 
  - Zero-DCE: 2-4小时
  - IGDFormer: 4-8小时  
  - Retinexformer: 8-12小时

- **模型大小**:
  - Zero-DCE: ~1MB
  - IGDFormer: ~50MB
  - Retinexformer: ~100MB

- **效果对比**: Retinexformer > IGDFormer > Zero-DCE (通常情况下)

开始实验吧！建议从Zero-DCE开始，逐步尝试更复杂的方法。
