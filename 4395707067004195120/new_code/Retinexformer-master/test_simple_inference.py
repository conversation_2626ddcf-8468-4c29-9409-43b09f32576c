#!/usr/bin/env python3
"""
Retinexformer简化推理脚本 - 基于官方test_from_dataset.py修改
只需要低光照图像，不需要标签
"""

import numpy as np
import os
import argparse
from tqdm import tqdm
import cv2
import torch
import torch.nn as nn
import torch.nn.functional as F
from natsort import natsorted
from glob import glob
from skimage import img_as_ubyte
import yaml

from basicsr.models import create_model
from basicsr.utils.options import parse
import Enhancement.utils as utils

def main():
    parser = argparse.ArgumentParser(description='Retinexformer纯推理测试')
    
    parser.add_argument('--input_dir', default='../train_data/in/', type=str, help='输入图像目录')
    parser.add_argument('--output_dir', default='./inference_results/', type=str, help='输出目录')
    parser.add_argument('--opt', type=str, default='Options/RetinexFormer_Custom.yml', help='配置文件路径')
    parser.add_argument('--weights', default='experiments/Enhancement_RetinexFormer_Custom/models/net_g_best.pth', type=str, help='权重文件路径')
    parser.add_argument('--gpus', type=str, default="0", help='GPU设备')
    
    args = parser.parse_args()
    
    print("🚀 Retinexformer简化推理测试")
    print("="*50)
    
    # 设置GPU
    os.environ['CUDA_VISIBLE_DEVICES'] = args.gpus
    print(f'使用GPU: {args.gpus}')
    
    # 检查输入目录
    if not os.path.exists(args.input_dir):
        print(f"❌ 输入目录不存在: {args.input_dir}")
        return
    
    # 检查权重文件
    if not os.path.exists(args.weights):
        print(f"❌ 权重文件不存在: {args.weights}")
        # 显示可用权重
        weights_dir = os.path.dirname(args.weights)
        if os.path.exists(weights_dir):
            print("可用的权重文件:")
            for f in os.listdir(weights_dir):
                if f.endswith('.pth'):
                    print(f"  - {os.path.join(weights_dir, f)}")
        return
    
    # 检查配置文件
    if not os.path.exists(args.opt):
        print(f"❌ 配置文件不存在: {args.opt}")
        return
    
    # 获取输入图像
    input_paths = natsorted(
        glob(os.path.join(args.input_dir, '*.png')) + 
        glob(os.path.join(args.input_dir, '*.jpg')) +
        glob(os.path.join(args.input_dir, '*.jpeg')) +
        glob(os.path.join(args.input_dir, '*.bmp'))
    )
    
    if len(input_paths) == 0:
        print(f"❌ 在 {args.input_dir} 中没有找到图像文件")
        return
    
    print(f"📊 找到 {len(input_paths)} 张输入图像")
    
    # 加载配置
    print("📋 加载配置...")
    try:
        opt = parse(args.opt, is_train=False)
        opt['dist'] = False
        print("✅ 配置加载成功")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 创建模型
    print("🤖 加载模型...")
    try:
        model_restoration = create_model(opt).net_g
        
        # 加载权重
        checkpoint = torch.load(args.weights, map_location='cpu')
        
        if 'params' in checkpoint:
            model_restoration.load_state_dict(checkpoint['params'])
        elif 'state_dict' in checkpoint:
            model_restoration.load_state_dict(checkpoint['state_dict'])
        else:
            model_restoration.load_state_dict(checkpoint)
        
        print(f"✅ 模型加载成功")
        print(f"使用权重: {args.weights}")
        
        model_restoration.cuda()
        model_restoration = nn.DataParallel(model_restoration)
        model_restoration.eval()
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"📁 结果保存到: {args.output_dir}")
    
    # 开始推理
    print("🔍 开始推理...")
    factor = 4
    
    with torch.inference_mode():
        for inp_path in tqdm(input_paths, desc="推理进度"):
            try:
                torch.cuda.ipc_collect()
                torch.cuda.empty_cache()
                
                # 加载图像
                img = np.float32(utils.load_img(inp_path)) / 255.
                img = torch.from_numpy(img).permute(2, 0, 1)
                input_ = img.unsqueeze(0).cuda()
                
                # Padding确保图像尺寸是4的倍数
                b, c, h, w = input_.shape
                H, W = ((h + factor) // factor) * factor, ((w + factor) // factor) * factor
                padh = H - h if h % factor != 0 else 0
                padw = W - w if w % factor != 0 else 0
                input_ = F.pad(input_, (0, padw, 0, padh), 'reflect')
                
                # 推理
                if h < 3000 and w < 3000:
                    restored = model_restoration(input_)
                else:
                    # 对于大图像，分块处理
                    input_1 = input_[:, :, :, 1::2]
                    input_2 = input_[:, :, :, 0::2]
                    restored_1 = model_restoration(input_1)
                    restored_2 = model_restoration(input_2)
                    restored = torch.zeros_like(input_)
                    restored[:, :, :, 1::2] = restored_1
                    restored[:, :, :, 0::2] = restored_2
                
                # 去除padding
                restored = restored[:, :, :h, :w]
                
                # 转换为numpy
                restored = torch.clamp(restored, 0, 1).cpu().detach().permute(0, 2, 3, 1).squeeze(0).numpy()
                
                # 保存结果
                input_name = os.path.splitext(os.path.basename(inp_path))[0]
                output_path = os.path.join(args.output_dir, f"{input_name}_enhanced.png")
                utils.save_img(output_path, img_as_ubyte(restored))
                
            except Exception as e:
                print(f"❌ 处理 {os.path.basename(inp_path)} 失败: {e}")
                continue
    
    print("\n" + "="*50)
    print("🎉 推理完成!")
    print(f"📊 处理图像数量: {len(input_paths)}")
    print(f"📁 结果保存在: {args.output_dir}")
    print("="*50)

if __name__ == "__main__":
    main()
