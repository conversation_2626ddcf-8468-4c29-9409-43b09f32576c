#!/usr/bin/env python3
"""
Retinexformer纯推理测试脚本 - 只需要低光照图像，不需要标签
"""

import torch
import torch.nn.functional as F
import cv2
import os
import glob
import numpy as np
from tqdm import tqdm
import argparse
from datetime import datetime
import yaml

# 添加basicsr路径
import sys
sys.path.append('.')

from basicsr.models import create_model
from basicsr.utils import tensor2img

def load_image(image_path, device):
    """加载单张图像"""
    # 读取图像
    img = cv2.imread(image_path)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img = img.astype(np.float32) / 255.0
    
    # 转换为tensor
    img_tensor = torch.from_numpy(img).permute(2, 0, 1).unsqueeze(0).to(device)
    return img_tensor

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def test_retinexformer_inference():
    """Retinexformer纯推理测试"""
    
    parser = argparse.ArgumentParser(description="Retinexformer纯推理测试")
    parser.add_argument('--input_dir', default='../train_data/in/', type=str, help='输入图像目录')
    parser.add_argument('--config', default='Options/RetinexFormer_Custom.yml', type=str, help='配置文件路径')
    parser.add_argument('--weights_path', default='experiments/Enhancement_RetinexFormer_Custom/models/net_g_best.pth', type=str, help='模型权重路径')
    parser.add_argument('--output_dir', default='./inference_results', type=str, help='结果保存目录')
    
    args = parser.parse_args()
    
    print("🚀 Retinexformer纯推理测试")
    print("="*50)
    
    # 检查输入目录
    if not os.path.exists(args.input_dir):
        print(f"❌ 输入目录不存在: {args.input_dir}")
        return
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        return
    
    # 检查权重文件
    if not os.path.exists(args.weights_path):
        print(f"❌ 权重文件不存在: {args.weights_path}")
        print("可用的权重文件:")
        weights_dir = os.path.dirname(args.weights_path)
        if os.path.exists(weights_dir):
            for f in os.listdir(weights_dir):
                if f.endswith('.pth'):
                    print(f"  - {os.path.join(weights_dir, f)}")
        return
    
    # 获取所有图像文件
    image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(args.input_dir, ext)))
        image_files.extend(glob.glob(os.path.join(args.input_dir, ext.upper())))
    
    image_files.sort()
    
    if len(image_files) == 0:
        print(f"❌ 在 {args.input_dir} 中没有找到图像文件")
        return
    
    print(f"📊 找到 {len(image_files)} 张输入图像")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 加载配置
    print("📋 加载配置...")
    try:
        opt = load_config(args.config)
        
        # 修改配置以适应推理
        opt['num_gpu'] = 1 if torch.cuda.is_available() else 0
        opt['dist'] = False
        opt['is_train'] = False  # 🎯 添加推理模式标志
        opt['path']['pretrain_network_g'] = args.weights_path
        opt['path']['strict_load_g'] = True

        # 添加其他必要的推理配置
        if 'datasets' in opt:
            del opt['datasets']  # 推理时不需要数据集配置
        if 'train' in opt:
            del opt['train']     # 推理时不需要训练配置
        if 'val' in opt:
            del opt['val']       # 推理时不需要验证配置
        
        print(f"✅ 配置加载成功")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 创建模型
    print("🤖 加载模型...")
    try:
        # 方法1: 尝试直接导入RetinexFormer
        net_g = None
        try:
            from basicsr.archs.retinexformer_arch import RetinexFormer
            # 从配置中获取网络参数
            network_opt = opt['network_g']
            net_g = RetinexFormer(
                in_channels=network_opt['in_channels'],
                out_channels=network_opt['out_channels'],
                n_feat=network_opt['n_feat'],
                stage=network_opt['stage'],
                num_blocks=network_opt['num_blocks']
            )
            print("✅ 使用直接导入方式创建模型")
        except ImportError:
            # 方法2: 使用create_model但添加必要配置
            print("📋 直接导入失败，使用create_model...")
            opt['train'] = {'optim_g': {'type': 'Adam', 'lr': 0.0001}}
            opt['val'] = {'val_freq': 1000}
            model = create_model(opt)
            net_g = model.net_g
            print("✅ 使用create_model创建模型")

        # 加载权重
        checkpoint = torch.load(args.weights_path, map_location='cpu')
        if 'params' in checkpoint:
            net_g.load_state_dict(checkpoint['params'], strict=True)
        elif 'state_dict' in checkpoint:
            net_g.load_state_dict(checkpoint['state_dict'], strict=True)
        else:
            net_g.load_state_dict(checkpoint, strict=True)

        net_g.to(device)
        net_g.eval()

        print(f"✅ 模型权重加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        print(f"错误详情: {str(e)}")
        return
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(args.output_dir, f"Retinexformer_inference_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"📁 结果保存到: {output_dir}")
    
    # 开始推理
    print("🔍 开始推理...")

    with torch.no_grad():
        for image_path in tqdm(image_files, desc="推理进度"):
            try:
                # 加载图像
                input_tensor = load_image(image_path, device)

                # 推理
                output_tensor = net_g(input_tensor)

                # 转换为图像
                output_img = tensor2img(output_tensor, rgb2bgr=True, out_type=np.uint8)

                # 保存结果
                input_name = os.path.splitext(os.path.basename(image_path))[0]
                output_path = os.path.join(output_dir, f"{input_name}_enhanced.png")
                cv2.imwrite(output_path, output_img)

            except Exception as e:
                print(f"❌ 处理 {os.path.basename(image_path)} 失败: {e}")
                continue
    
    print("\n" + "="*50)
    print("🎉 推理完成!")
    print(f"📊 处理图像数量: {len(image_files)}")
    print(f"📁 结果保存在: {output_dir}")
    print("="*50)

if __name__ == "__main__":
    test_retinexformer_inference()
