# Test configuration for Custom dataset
name: Test_RetinexFormer_Custom
model_type: ImageCleanModel
scale: 1
num_gpu: 1
manual_seed: 100

# dataset settings for testing
datasets:
  test:
    name: TestSet
    type: Dataset_PairedImage
    dataroot_gt: ../data/Custom/Test/target
    dataroot_lq: ../data/Custom/Test/input
    io_backend:
      type: disk

# network structures
network_g:
  type: RetinexFormer
  in_channels: 3
  out_channels: 3
  n_feat: 40
  stage: 1
  num_blocks: [1,2,2]

# path
path:
  pretrain_network_g: ~
  strict_load_g: true

# validation settings
val:
  window_size: 4
  save_img: true          # 保存增强后的图像
  suffix: ~               # 输出文件后缀
  rgb2bgr: true
  use_image: true
  max_minibatch: 8

  metrics:
    psnr:
      type: calculate_psnr
      crop_border: 0
      test_y_channel: false
    ssim:
      type: calculate_ssim
      crop_border: 0
      test_y_channel: false
