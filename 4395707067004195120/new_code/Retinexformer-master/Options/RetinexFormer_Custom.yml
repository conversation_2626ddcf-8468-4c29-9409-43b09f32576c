# general settings
name: Enhancement_RetinexFormer_Custom
model_type: ImageCleanModel
scale: 1
num_gpu: 1  # set num_gpu: 0 for cpu mode
manual_seed: 100

# dataset and data loader settings
datasets:
  train:
    name: TrainSet
    type: Dataset_PairedImage
    dataroot_gt: ../data/Custom/Train/target
    dataroot_lq: ../data/Custom/Train/input
    geometric_augs: true

    filename_tmpl: '{}'
    io_backend:
      type: disk

    # data loader
    use_shuffle: true
    num_worker_per_gpu: 4
    batch_size_per_gpu: 4  # 🎯 调整这里改变batch大小 (2/4/8)

    ### ------- Training on single fixed-patch size 128x128---------
    mini_batch_sizes: [4]   # 保持与batch_size_per_gpu一致
    iters: [75000]          # 🎯 与total_iter保持一致
    gt_size: 128
    gt_sizes: [128]
    ### ------------------------------------------------------------

    dataset_enlarge_ratio: 1
    prefetch_mode: ~

  val:
    name: ValSet
    type: Dataset_PairedImage
    dataroot_gt: ../data/Custom/Test/target
    dataroot_lq: ../data/Custom/Test/input
    io_backend:
      type: disk

# network structures
network_g:
  type: RetinexFormer
  in_channels: 3
  out_channels: 3
  n_feat: 40
  stage: 1
  num_blocks: [1,2,2]


# path
path:
  pretrain_network_g: ~
  strict_load_g: true
  resume_state: ~

# training settings
train:
  total_iter: 75000  # 🎯 总迭代次数 (约200个epoch)
  warmup_iter: -1 # no warm up
  use_grad_clip: true

  # 🎯 学习率调度 - 分为两个阶段
  # 第1阶段: 固定学习率 3e-4，持续23k次迭代
  # 第2阶段: 余弦退火 3e-4→1e-6，持续52k次迭代
  scheduler:
    type: CosineAnnealingRestartCyclicLR
    periods: [23000, 52000]       # 🎯 调整学习率周期
    restart_weights: [1,1]
    eta_mins: [0.0003,0.000001]
  
  mixing_augs:
    mixup: true
    mixup_beta: 1.2
    use_identity: true

  optim_g:
    type: Adam
    lr: !!float 2e-4
    # weight_decay: !!float 1e-4
    betas: [0.9, 0.999]
  
  # losses
  pixel_opt:
    type: L1Loss
    loss_weight: 1
    reduction: mean

# validation settings
val:
  window_size: 4
  val_freq: !!float 2e3
  save_img: false
  rgb2bgr: true
  use_image: false
  max_minibatch: 8

  metrics:
    psnr: # metric name, can be arbitrary
      type: calculate_psnr
      crop_border: 0
      test_y_channel: false

# logging settings
logger:
  print_freq: 500
  save_checkpoint_freq: !!float 2e3
  use_tb_logger: true
  wandb:
    project: low_light_custom
    resume_id: ~

# dist training settings
dist_params:
  backend: nccl
  port: 29500
