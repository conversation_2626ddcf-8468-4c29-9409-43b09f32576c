# 📊 Retinexformer参数控制详解

## 🎯 **核心控制参数**

### **1. 训练数量控制**
```yaml
# training settings
train:
  total_iter: 75000  # 🎯 总迭代次数 (这是主要控制参数)
```

### **2. Batch Size控制**
```yaml
# dataset settings
datasets:
  train:
    batch_size_per_gpu: 4     # 🎯 每个GPU的batch大小
    mini_batch_sizes: [4]     # 保持与batch_size_per_gpu一致
```

## 📈 **迭代次数与Epoch的换算**

假设您的数据集：
- 训练图像数量: 1500张
- batch_size_per_gpu: 4

**计算公式**:
```
每个epoch的迭代次数 = 训练图像数量 ÷ batch_size_per_gpu
                  = 1500 ÷ 4 = 375次迭代

总epoch数 = total_iter ÷ 每个epoch迭代次数
         = 75000 ÷ 375 = 200个epoch
```

## 🛠️ **不同训练需求的配置**

### **快速测试 (1-2小时)**
```yaml
train:
  total_iter: 15000        # 约40个epoch
  scheduler:
    periods: [5000, 10000] # 调整学习率周期

datasets:
  train:
    batch_size_per_gpu: 8  # 增大batch_size
    mini_batch_sizes: [8]
```

### **标准训练 (8-12小时)**
```yaml
train:
  total_iter: 75000         # 约200个epoch
  scheduler:
    periods: [23000, 52000] # 当前设置

datasets:
  train:
    batch_size_per_gpu: 4   # 平衡设置
    mini_batch_sizes: [4]
```

### **完整训练 (16-24小时)**
```yaml
train:
  total_iter: 150000        # 约400个epoch
  scheduler:
    periods: [46000, 104000] # 原始设置

datasets:
  train:
    batch_size_per_gpu: 2   # 如果显存不够
    mini_batch_sizes: [2]
```

## 🔧 **其他重要参数**

### **验证频率**
```yaml
val:
  val_freq: !!float 2e3     # 每2000次迭代验证一次
```

### **模型保存频率**
```yaml
logger:
  save_checkpoint_freq: !!float 2e3  # 每2000次迭代保存一次
```

### **日志显示频率**
```yaml
logger:
  print_freq: 500           # 每500次迭代显示一次
```

## 📊 **显存使用参考**

| batch_size_per_gpu | 显存需求 | 训练速度 | 推荐GPU |
|-------------------|---------|---------|---------|
| 2 | ~6GB | 慢 | GTX 1080 |
| 4 | ~10GB | 中等 | RTX 3080 |
| 8 | ~18GB | 快 | RTX 4090 |
| 16 | ~32GB | 很快 | A100 |

## 🎛️ **如何修改配置**

### **修改训练长度**
```bash
# 编辑配置文件
vim Options/RetinexFormer_Custom.yml

# 找到并修改
train:
  total_iter: 30000  # 改为您想要的迭代次数
```

### **修改batch大小**
```bash
# 同时修改这两个参数
datasets:
  train:
    batch_size_per_gpu: 8
    mini_batch_sizes: [8]
```

### **修改学习率周期**
```bash
# 按比例调整periods
# 如果total_iter改为30000，则periods应该改为:
scheduler:
  periods: [9000, 21000]  # 保持3:7的比例
```

## 🚀 **推荐配置组合**

### **新手测试**
```yaml
total_iter: 15000
batch_size_per_gpu: 8
periods: [5000, 10000]
```

### **正常训练**
```yaml
total_iter: 75000
batch_size_per_gpu: 4
periods: [23000, 52000]
```

### **追求最佳效果**
```yaml
total_iter: 150000
batch_size_per_gpu: 2
periods: [46000, 104000]
```

## 💡 **实用技巧**

1. **显存不够**: 减小batch_size_per_gpu
2. **训练太慢**: 增大batch_size_per_gpu
3. **想快速测试**: 减小total_iter
4. **追求最佳效果**: 增大total_iter

记住：Retinexformer不使用epoch概念，一切都基于迭代次数(iterations)控制！
