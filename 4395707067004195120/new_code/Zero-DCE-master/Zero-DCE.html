<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!-- saved from url=(0048)https://xinntao.github.io/projects/EDVR#citation -->
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  
  <title>Zero-Reference Deep Curve Estimation for Low-Light Image Enhancement</title>
  <!--=================Meta tags==========================-->
  <meta name="robots" content="index,follow">
  <meta name="description" content="Zero-Reference Deep Curve Estimation for Low-Light Image Enhancement.">
  <meta name="keywords" content="Zero-Reference">
  <link rel="author" href="https://li-chongyi.github.io">
  <!--=================js==========================-->
  <link href="./Zero-DCE_files/css.css" rel="stylesheet" type="text/css">
  <link rel="stylesheet" type="text/css" href="./Zero-DCE_files/project.css" media="screen">
  <script type="text/javascript" async="" src="./Zero-DCE_files/analytics.js.download"></script><script src="./Zero-DCE_files/effect.js.download"></script>
  <!-- Latex -->
  <script type="text/x-mathjax-config;executed=true">
    MathJax.Hub.Config({
        tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]},
        TeX: { equationNumbers: { autoNumber: "AMS" } },
      });
      </script>
  <script type="text/javascript" async="" src="./Zero-DCE_files/latest.js.download">
    </script>
  <!--=================Google Analytics==========================-->
  <script async="" src="./Zero-DCE_files/js"></script>
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'UA-129775907-1');
  </script>
<script type="text/javascript" async="" src="./Zero-DCE_files/MathJax.js.download"></script><style type="text/css">.MathJax_Hover_Frame {border-radius: .25em; -webkit-border-radius: .25em; -moz-border-radius: .25em; -khtml-border-radius: .25em; box-shadow: 0px 0px 15px #83A; -webkit-box-shadow: 0px 0px 15px #83A; -moz-box-shadow: 0px 0px 15px #83A; -khtml-box-shadow: 0px 0px 15px #83A; border: 1px solid #A6D ! important; display: inline-block; position: absolute}
.MathJax_Menu_Button .MathJax_Hover_Arrow {position: absolute; cursor: pointer; display: inline-block; border: 2px solid #AAA; border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px; -khtml-border-radius: 4px; font-family: 'Courier New',Courier; font-size: 9px; color: #F0F0F0}
.MathJax_Menu_Button .MathJax_Hover_Arrow span {display: block; background-color: #AAA; border: 1px solid; border-radius: 3px; line-height: 0; padding: 4px}
.MathJax_Hover_Arrow:hover {color: white!important; border: 2px solid #CCC!important}
.MathJax_Hover_Arrow:hover span {background-color: #CCC!important}
</style><style type="text/css">#MathJax_About {position: fixed; left: 50%; width: auto; text-align: center; border: 3px outset; padding: 1em 2em; background-color: #DDDDDD; color: black; cursor: default; font-family: message-box; font-size: 120%; font-style: normal; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; z-index: 201; border-radius: 15px; -webkit-border-radius: 15px; -moz-border-radius: 15px; -khtml-border-radius: 15px; box-shadow: 0px 10px 20px #808080; -webkit-box-shadow: 0px 10px 20px #808080; -moz-box-shadow: 0px 10px 20px #808080; -khtml-box-shadow: 0px 10px 20px #808080; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
#MathJax_About.MathJax_MousePost {outline: none}
.MathJax_Menu {position: absolute; background-color: white; color: black; width: auto; padding: 2px; border: 1px solid #CCCCCC; margin: 0; cursor: default; font: menu; text-align: left; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; z-index: 201; box-shadow: 0px 10px 20px #808080; -webkit-box-shadow: 0px 10px 20px #808080; -moz-box-shadow: 0px 10px 20px #808080; -khtml-box-shadow: 0px 10px 20px #808080; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
.MathJax_MenuItem {padding: 2px 2em; background: transparent}
.MathJax_MenuArrow {position: absolute; right: .5em; padding-top: .25em; color: #666666; font-size: .75em}
.MathJax_MenuActive .MathJax_MenuArrow {color: white}
.MathJax_MenuArrow.RTL {left: .5em; right: auto}
.MathJax_MenuCheck {position: absolute; left: .7em}
.MathJax_MenuCheck.RTL {right: .7em; left: auto}
.MathJax_MenuRadioCheck {position: absolute; left: 1em}
.MathJax_MenuRadioCheck.RTL {right: 1em; left: auto}
.MathJax_MenuLabel {padding: 2px 2em 4px 1.33em; font-style: italic}
.MathJax_MenuRule {border-top: 1px solid #CCCCCC; margin: 4px 1px 0px}
.MathJax_MenuDisabled {color: GrayText}
.MathJax_MenuActive {background-color: Highlight; color: HighlightText}
.MathJax_MenuDisabled:focus, .MathJax_MenuLabel:focus {background-color: #E8E8E8}
.MathJax_ContextMenu:focus {outline: none}
.MathJax_ContextMenu .MathJax_MenuItem:focus {outline: none}
#MathJax_AboutClose {top: .2em; right: .2em}
.MathJax_Menu .MathJax_MenuClose {top: -10px; left: -10px}
.MathJax_MenuClose {position: absolute; cursor: pointer; display: inline-block; border: 2px solid #AAA; border-radius: 18px; -webkit-border-radius: 18px; -moz-border-radius: 18px; -khtml-border-radius: 18px; font-family: 'Courier New',Courier; font-size: 24px; color: #F0F0F0}
.MathJax_MenuClose span {display: block; background-color: #AAA; border: 1.5px solid; border-radius: 18px; -webkit-border-radius: 18px; -moz-border-radius: 18px; -khtml-border-radius: 18px; line-height: 0; padding: 8px 0 6px}
.MathJax_MenuClose:hover {color: white!important; border: 2px solid #CCC!important}
.MathJax_MenuClose:hover span {background-color: #CCC!important}
.MathJax_MenuClose:hover:focus {outline: none}
</style><style type="text/css">.MathJax_Preview .MJXf-math {color: inherit!important}
</style><style type="text/css">.MJX_Assistive_MathML {position: absolute!important; top: 0; left: 0; clip: rect(1px, 1px, 1px, 1px); padding: 1px 0 0 0!important; border: 0!important; height: 1px!important; width: 1px!important; overflow: hidden!important; display: block!important; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none}
.MJX_Assistive_MathML.MJX_Assistive_MathML_Block {width: 100%!important}
</style><style type="text/css">#MathJax_Zoom {position: absolute; background-color: #F0F0F0; overflow: auto; display: block; z-index: 301; padding: .5em; border: 1px solid black; margin: 0; font-weight: normal; font-style: normal; text-align: left; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; -webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box; box-shadow: 5px 5px 15px #AAAAAA; -webkit-box-shadow: 5px 5px 15px #AAAAAA; -moz-box-shadow: 5px 5px 15px #AAAAAA; -khtml-box-shadow: 5px 5px 15px #AAAAAA; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
#MathJax_ZoomOverlay {position: absolute; left: 0; top: 0; z-index: 300; display: inline-block; width: 100%; height: 100%; border: 0; padding: 0; margin: 0; background-color: white; opacity: 0; filter: alpha(opacity=0)}
#MathJax_ZoomFrame {position: relative; display: inline-block; height: 0; width: 0}
#MathJax_ZoomEventTrap {position: absolute; left: 0; top: 0; z-index: 302; display: inline-block; border: 0; padding: 0; margin: 0; background-color: white; opacity: 0; filter: alpha(opacity=0)}
</style><style type="text/css">.MathJax_Preview {color: #888}
#MathJax_Message {position: fixed; left: 1em; bottom: 1.5em; background-color: #E6E6E6; border: 1px solid #959595; margin: 0px; padding: 2px 8px; z-index: 102; color: black; font-size: 80%; width: auto; white-space: nowrap}
#MathJax_MSIE_Frame {position: absolute; top: 0; left: 0; width: 0px; z-index: 101; border: 0px; margin: 0px; padding: 0px}
.MathJax_Error {color: #CC0000; font-style: italic}
</style><style type="text/css">.MJXp-script {font-size: .8em}
.MJXp-right {-webkit-transform-origin: right; -moz-transform-origin: right; -ms-transform-origin: right; -o-transform-origin: right; transform-origin: right}
.MJXp-bold {font-weight: bold}
.MJXp-italic {font-style: italic}
.MJXp-scr {font-family: MathJax_Script,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-frak {font-family: MathJax_Fraktur,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-sf {font-family: MathJax_SansSerif,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-cal {font-family: MathJax_Caligraphic,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-mono {font-family: MathJax_Typewriter,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-largeop {font-size: 150%}
.MJXp-largeop.MJXp-int {vertical-align: -.2em}
.MJXp-math {display: inline-block; line-height: 1.2; text-indent: 0; font-family: 'Times New Roman',Times,STIXGeneral,serif; white-space: nowrap; border-collapse: collapse}
.MJXp-display {display: block; text-align: center; margin: 1em 0}
.MJXp-math span {display: inline-block}
.MJXp-box {display: block!important; text-align: center}
.MJXp-box:after {content: " "}
.MJXp-rule {display: block!important; margin-top: .1em}
.MJXp-char {display: block!important}
.MJXp-mo {margin: 0 .15em}
.MJXp-mfrac {margin: 0 .125em; vertical-align: .25em}
.MJXp-denom {display: inline-table!important; width: 100%}
.MJXp-denom > * {display: table-row!important}
.MJXp-surd {vertical-align: top}
.MJXp-surd > * {display: block!important}
.MJXp-script-box > *  {display: table!important; height: 50%}
.MJXp-script-box > * > * {display: table-cell!important; vertical-align: top}
.MJXp-script-box > *:last-child > * {vertical-align: bottom}
.MJXp-script-box > * > * > * {display: block!important}
.MJXp-mphantom {visibility: hidden}
.MJXp-munderover, .MJXp-munder {display: inline-table!important}
.MJXp-over {display: inline-block!important; text-align: center}
.MJXp-over > * {display: block!important}
.MJXp-munderover > *, .MJXp-munder > * {display: table-row!important}
.MJXp-mtable {vertical-align: .25em; margin: 0 .125em}
.MJXp-mtable > * {display: inline-table!important; vertical-align: middle}
.MJXp-mtr {display: table-row!important}
.MJXp-mtd {display: table-cell!important; text-align: center; padding: .5em 0 0 .5em}
.MJXp-mtr > .MJXp-mtd:first-child {padding-left: 0}
.MJXp-mtr:first-child > .MJXp-mtd {padding-top: 0}
.MJXp-mlabeledtr {display: table-row!important}
.MJXp-mlabeledtr > .MJXp-mtd:first-child {padding-left: 0}
.MJXp-mlabeledtr:first-child > .MJXp-mtd {padding-top: 0}
.MJXp-merror {background-color: #FFFF88; color: #CC0000; border: 1px solid #CC0000; padding: 1px 3px; font-style: normal; font-size: 90%}
.MJXp-scale0 {-webkit-transform: scaleX(.0); -moz-transform: scaleX(.0); -ms-transform: scaleX(.0); -o-transform: scaleX(.0); transform: scaleX(.0)}
.MJXp-scale1 {-webkit-transform: scaleX(.1); -moz-transform: scaleX(.1); -ms-transform: scaleX(.1); -o-transform: scaleX(.1); transform: scaleX(.1)}
.MJXp-scale2 {-webkit-transform: scaleX(.2); -moz-transform: scaleX(.2); -ms-transform: scaleX(.2); -o-transform: scaleX(.2); transform: scaleX(.2)}
.MJXp-scale3 {-webkit-transform: scaleX(.3); -moz-transform: scaleX(.3); -ms-transform: scaleX(.3); -o-transform: scaleX(.3); transform: scaleX(.3)}
.MJXp-scale4 {-webkit-transform: scaleX(.4); -moz-transform: scaleX(.4); -ms-transform: scaleX(.4); -o-transform: scaleX(.4); transform: scaleX(.4)}
.MJXp-scale5 {-webkit-transform: scaleX(.5); -moz-transform: scaleX(.5); -ms-transform: scaleX(.5); -o-transform: scaleX(.5); transform: scaleX(.5)}
.MJXp-scale6 {-webkit-transform: scaleX(.6); -moz-transform: scaleX(.6); -ms-transform: scaleX(.6); -o-transform: scaleX(.6); transform: scaleX(.6)}
.MJXp-scale7 {-webkit-transform: scaleX(.7); -moz-transform: scaleX(.7); -ms-transform: scaleX(.7); -o-transform: scaleX(.7); transform: scaleX(.7)}
.MJXp-scale8 {-webkit-transform: scaleX(.8); -moz-transform: scaleX(.8); -ms-transform: scaleX(.8); -o-transform: scaleX(.8); transform: scaleX(.8)}
.MJXp-scale9 {-webkit-transform: scaleX(.9); -moz-transform: scaleX(.9); -ms-transform: scaleX(.9); -o-transform: scaleX(.9); transform: scaleX(.9)}
.MathJax_PHTML .noError {vertical-align: ; font-size: 90%; text-align: left; color: black; padding: 1px 3px; border: 1px solid}
</style></head>

<body><div id="MathJax_Message" style="display: none;"></div>
  <div id="content">
    <div id="content-inner">
      <div class="section head">
        <h1>
          <font color="LightSkyBlue">Zero</font>-Reference <font color="LightSkyBlue">D</font>eep <font color="LightSkyBlue">C</font>urve <font color="LightSkyBlue">E</font>stimation (<font color="LightSkyBlue">Zero-DCE</font>)
        </h1>
        <h1>for Low-Light Image Enhancement</h1>
        <!--=================Authors==========================-->
        <div class="authors">
          Chunle Guo <sup>1</sup> &nbsp;
          <a href="https://li-chongyi.github.io/" target="_blank">Chongyi Li</a> <sup>2</sup> &nbsp;
          Jichang Guo <sup>1</sup> &nbsp;
          <a href="http://personal.ie.cuhk.edu.hk/~ccloy/index.html" target="_blank">Chen Change Loy</a> <sup>3</sup>&nbsp;
          <a href="https://sites.google.com/site/junhuihoushomepage/" target="_blank">Junhui Hou</a> <sup>2</sup>&nbsp;
          <a href="https://www.cs.cityu.edu.hk/~cssamk/research_group/index.html" target="_blank">Sam Kwong</a> <sup>2</sup>&nbsp;
          <a href="https://rmcong.github.io/" target="_blank">Runmin Cong</a> <sup>4</sup>
        </div>

        <div class="affiliations ">
          <sup>1</sup> Tianjin University, Tianjin, China <br>
          <sup>2</sup> City University of Hong Kong, Hong Kong<br>
          <sup>3</sup> Nanyang Technological University, Singapore<br>
          <sup>4</sup> Beijing Jiaotong University, Beijing, China
        </div>
        <!--=================Tabs==========================
        <ul id="tabs">
          <li><a href="https://li-chongyi.github.io/Zero.html#materials" name="#tab1">Materials</a></li>
          <li><a href="https://li-chongyi.github.io/Zero.html#pcd_tsa" name="#tab2">PCD &amp; TSA</a></li>
          <li><a href="https://li-chongyi.github.io/Zero.html#ablations" name="#tab3">Ablations</a></li>
          <li><a href="https://li-chongyi.github.io/Zero.html#results" name="#tab4">Results</a></li>
          <li><a href="https://li-chongyi.github.io/Zero.html#citation" name="#tab5">Citation</a></li>
      </ul>-->
      </div>
      <br>
      <div class="section abstract">
        <h2>Abstract</h2>
        <br>
        <p>
          <div style="text-align: justify; display: block; margin-right: auto;">
          <p>The paper presents a novel method, <b>Zero-Reference Deep Curve Estimation (Zero-DCE)</b>, which formulates light enhancement as a task of image-specific curve estimation with a deep network.  Our method trains a lightweight deep network, DCE-Net, to estimate pixel-wise and high-order curves for dynamic range adjustment of a given image. The curve estimation is specially designed, considering pixel value range, monotonicity, and differentiability. Zero-DCE is appealing in its relaxed assumption on reference images, i.e., it does not require any paired or unpaired data during training. This is achieved through a set of carefully formulated non-reference loss functions, which implicitly measure the enhancement quality and drive the learning of the network. Our method is efficient as image enhancement can be achieved by an intuitive and simple nonlinear curve mapping. Despite its simplicity, we show that it generalizes well to diverse lighting conditions. Extensive experiments on various benchmarks demonstrate the advantages of our method over state-of-the-art methods qualitatively and quantitatively. Furthermore, the potential benefits of our Zero-DCE to face detection in the dark are discussed.</p>
        </p>
      </div>
      
      <!--=================Teasers==========================-->
      <div id="img_intro_examples" class="img_container">
       <ol>
          <h2>Pipeline</h2>
        </ol>
          <ol>
        <center>
          <div class="leftView">
            <div class="mask" style="width:80px;height:80px"></div>
            <img class="std" src="./Zero-DCE_files/framework.png">
          </div>
        </center>
         </ol>
      </div>
      <div class="section">
       <ol>
        <p><b>The pipeline of our method</b>. (a) The framework of Zero-DCE. A DCE-Net is devised to estimate a set of best-fitting Light-Enhancement curves (LE-curves: LE(I(x);&#945)=I(x)+&#945I(x)(1-I(x))) to iteratively enhance a given input image. (b, c) LE-curves with different adjustment parameters <i>&#945</i> and numbers of iteration <i>n</i>. In (c), <i>&#945<sub>1</sub></i>, <i>&#945<sub>2</sub></i>, and <i>&#945<sub>3</sub></i> are equal to -1 while <i>n</i> is equal to 4. In each subfigure, the horizontal axis represents the input pixel values while the vertical axis represents the output pixel values.</p>
         </ol>
      </div>
      <!--=================Highlights==========================-->
      <div class="section abstract">
        <h2>Highlights</h2>
        <ol>
          <li><p>We propose the first low-light enhancement network that is <b>independent of paired and unpaired training data</b>, thus avoiding the risk of overfitting. As a result, our method generalizes well to various lighting conditions.</p></li>
          <li><p>We design a simple and lightweight deep network that is able to <b>approximate pixel-wise and higher-order curves by iteratively applying itself</b>. Such image-specific curves can effectively perform mapping within a wide dynamic range.</p></li>
          <li><p>We show the potential of training a deep image enhancement model in the absence of reference images through task-specific non-reference loss functions that indirectly evaluate enhancement quality.  It is capable of processing images in real-time (<b>about 500 FPS for images of size 640*480*3 on GPU) and takes only 30 minutes for training</b>.</p></li>
        </ol>
      </div>
      <!--=================Applications==========================-->
      <div class="section" ,="" id="results">
        <h2>Results</h2>
        <!--=================*******==========================-->
        <h3>1. Visual Comparisons on Typical Low-light Images</h3>
        <div id="vid4" class="img_container">
          <center>
            <div class="leftView">
              <div class="mask" style="width:100px;height:100px"></div>
              <img class="std" src="./Zero-DCE_files/results.png">
            </div>
          </center>
        </div>
        <br>
        <!--=================*******==========================-->
        <h3>2. Visual Face Detection Results Before and After Enanced by Zero-DCE</h3>
        <div id="vimeo90k" class="img_container">
          <center>
            <div class="leftView">
              <div class="mask" style="width:70px;height:70px"></div>
              <img class="small" src="./Zero-DCE_files/face.png">
            </div>
          </center>
        </div>
        <br>
        <!--=================*******==========================-->
        <h3>3. Real Low-light Video with Variational Illumination Enanced by Zero-DCE</h3>
          <div class="section demo">
    <br> <center>
      <iframe width="800" height="500" src="https://www.youtube.com/embed/NO7Evfra-Tk" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe> </center> <br>
  </div>
      <!--=================*******==========================-->
        <h3>4. Self-training (taking first 100 frames as training data) for Low-light Video Enhancement</h3>
          <div class="section demo">
    <br> <center>
      <iframe width="800" height="500" src=" https://www.youtube.com/embed/Z-cRayugi6g" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe> </center> <br>
  </div>
      <!--=================Ablation studies==========================-->
      <div class="section" ,="" id="ablations">
        <h2>Ablation Studies</h2>
        <h3>1. Contribution of Each Loss</h3>
        <div id="vid4" class="img_container">
          <center>
            <div class="leftView">
              <div class="mask" style="width:70px;height:70px"></div>
              <img class="std" src="./Zero-DCE_files/loss.png">
            </div>
          </center>
        </div>
        <br>
        <p>
Ablation study of the contribution of each loss (spatial consistency loss <i>L<sub>spa</sub></i>, exposure control loss <i>L<sub>exp</sub></i>, color constancy loss <i>L<sub>col</sub></i>, illumination smoothness loss <i>L<sub>tv<sub>A</sub></sub></i>).
        </p>
        <!--=================*******==========================-->
        <h3>2. Effect of Parameter Settings</h3>
        <div id="vimeo90k" class="img_container">
          <center>
            <div class="leftView">
              <div class="mask" style="width:10px;height:70px"></div>
              <img class="std" src="./Zero-DCE_files/parameter.png">
            </div>
          </center>
        </div>
        <br>
        <p>
        Ablation study of the effect of parameter settings. <i>l-f-n</i> represents the proposed Zero-DCE with <i>l</i> convolutional layers, <i>f</i> feature maps of each layer (except the last layer), and <i>n</i> iterations.
        </p>
        <!--=================*******==========================-->
        <h3>3. Impact of Training Data</h3>
        <div id="vimeo90k" class="img_container">
          <center>
            <div class="leftView">
              <div class="mask" style="width:10px;height:70px"></div>
              <img class="std" src="./Zero-DCE_files/training.png">
            </div>
          </center>
        </div>
        <br>
        <p>
To test the impact of training data, we retrain the Zero-DCE on  different datasets: 1) only 900 low-light images out of 2,422 images in the original training set (Zero-DCE<i><sub>Low</sub></i>), 2) 9,000 unlabeled low-light images provided in the DARK FACE dataset (Zero-DCE<i><sub>LargeL</sub></i>), and 3) 4800 multi-exposure images from the data augmented combination of Part1 and Part2 subsets in the SICE dataset (Zero-DCE<i><sub>LargeLH</sub></i>).
        </p>
        <!--=================*******==========================-->
        <h3>4. Advantage of Three-channel Adjustment</h3>
        <div id="vimeo90k" class="img_container">
          <center>
            <div class="leftView">
              <div class="mask" style="width:10px;height:70px"></div>
              <img class="std" src="./Zero-DCE_files/channel.png">
            </div>
          </center>
        </div>
        <br>
        <p>
Ablation study of the advantage of three-channel adjustment (RGB, CIE Lab, YCbCr color spaces).
        </p>
  
            <!--=================Materials==========================-->
      <div class="section materials" ,="" id="materials">
        <h2>Materials</h2>
        <table width="100%" align="center" border="none" cellspacing="0" cellpadding="30">
          <tbody><tr>
            <td width="20%">
              <center>
                <a href="https://arxiv.org/abs/2001.06826" target="_blank" class="imageLink"><img src="./Zero-DCE_files/paper.png" ,="" width="80%"></a><br><br>
                <a href="https://arxiv.org/abs/2001.06826" target="_blank">Paper</a>
              </center>
            </td>
            <td width="20%">
              <center>
                <a href="https://arxiv.org/abs/2001.06826" target="_blank" class="imageLink"><img src="./Zero-DCE_files/paper.png" ,="" width="80%"></a><br><br>
                <a href="https://arxiv.org/abs/2001.06826" target="_blank">Supplementary Material</a>
              </center>
            </td>
            <td width="20%">
              <center>
                <a href="https://github.com/Li-Chongyi/Zero-DCE" target="_blank" class="imageLink"><img src="./Zero-DCE_files/icon_github.png" ,="" width="40%"></a><br><br>
                <a href="https://github.com/Li-Chongyi/Zero-DCE" target="_blank">Code and Model</a>
              </center>
            </td>
          </tr>
        </tbody></table>
      </div>
      <!--=================Citation==========================-->
      <div class="section citation" ,="" id="citation">
        <h2>Citation</h2>
        <div class="section bibtex">
          <pre>@Article{Zero-DCE,
          author = {Guo, Chunle and Li, Chongyi and Guo, Jichang and Loy, Chen Change and Hou, Junhui and Kwong, Sam and Cong Runmin},
          title = {Zero-reference deep curve estimation for low-light image enhancement},
          journal = {arXiv preprint arXiv:2001.06826},
          year = {2020}
          }
          </pre>
        </div>
      </div>
      <!--=================Contact==========================-->
      <div class="section contact">
        <h2 id="contact">Contact</h2>
        <p>If you have any questions, please contact Chongyi Li at <strong><EMAIL></strong> or Chunle Guo at <strong><EMAIL></strong>.</p>
      </div>


</div></div></body></html>