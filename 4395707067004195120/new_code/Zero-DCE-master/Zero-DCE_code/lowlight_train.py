import torch
import torch.nn as nn
import torchvision
import torch.backends.cudnn as cudnn
import torch.optim
import os
import sys
import argparse
import time
import dataloader
import model
import Myloss
import numpy as np
import cv2
from datetime import datetime
from torchvision import transforms


def weights_init(m):
    classname = m.__class__.__name__
    if classname.find('Conv') != -1:
        m.weight.data.normal_(0.0, 0.02)
    elif classname.find('BatchNorm') != -1:
        m.weight.data.normal_(1.0, 0.02)
        m.bias.data.fill_(0)


def create_triangle_mask(img_shape, base_length):
    """随机生成一个等腰三角形的掩膜"""
    h, w = img_shape
    mask = np.zeros((h, w), dtype=np.float32)

    # 随机选择三角形顶点和底边中心
    apex_y = np.random.randint(0, h - base_length // 2)
    base_center_x = np.random.randint(base_length // 2, w - base_length // 2)

    # 计算三角形三个顶点坐标
    apex = (apex_y, base_center_x)
    base_left = (h - 1, base_center_x - base_length // 2)
    base_right = (h - 1, base_center_x + base_length // 2)

    # 使用 OpenCV 的 fillConvexPoly 函数填充三角形区域
    triangle_points = np.array([apex, base_left, base_right], dtype=np.int32)
    cv2.fillConvexPoly(mask, triangle_points, 1.0)

    return torch.from_numpy(mask).cuda()


def train(config):

	os.environ['CUDA_VISIBLE_DEVICES']='0'

	DCE_net = model.enhance_net_nopool().cuda()

	DCE_net.apply(weights_init)
	if config.load_pretrain == True:
	    DCE_net.load_state_dict(torch.load(config.pretrain_dir))
	train_dataset = dataloader.lowlight_loader(config.lowlight_images_path)		
	
	train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=config.train_batch_size, shuffle=True, num_workers=config.num_workers, pin_memory=True)



	L_color = Myloss.L_color()
	L_spa = Myloss.L_spa()

	L_exp = Myloss.L_exp(16,0.6)
	L_TV = Myloss.L_TV()
	L_local = Myloss.L_local_highlight(target_brightness_high=0.7, target_brightness_low=0.3)


	optimizer = torch.optim.Adam(DCE_net.parameters(), lr=config.lr, weight_decay=config.weight_decay)

	DCE_net.train()

	# 训练统计信息
	total_batches = len(train_loader)
	print(f"\n🚀 开始训练 Zero-DCE")
	print(f"📊 训练配置:")
	print(f"   - 总epoch数: {config.num_epochs}")
	print(f"   - 每epoch批次数: {total_batches}")
	print(f"   - 批次大小: {config.train_batch_size}")
	print(f"   - 学习率: {config.lr}")
	print(f"   - 数据路径: {config.lowlight_images_path}")
	print(f"   - 模型保存路径: {config.snapshots_folder}")
	print("="*60)

	# 记录训练开始时间
	training_start_time = time.time()

	for epoch in range(config.num_epochs):
		epoch_start_time = time.time()
		epoch_loss = 0.0
		epoch_loss_tv = 0.0
		epoch_loss_spa = 0.0
		epoch_loss_col = 0.0
		epoch_loss_exp = 0.0

		print(f"\n📈 Epoch [{epoch+1}/{config.num_epochs}] 开始训练...")

		for iteration, img_lowlight in enumerate(train_loader):

			highlight_mask = create_triangle_mask(img_lowlight.shape[2:], 100)

			img_lowlight = img_lowlight.cuda()

			enhanced_image_1,enhanced_image,A  = DCE_net(img_lowlight)

			Loss_TV = 200*L_TV(A)
			
			loss_spa = torch.mean(L_spa(enhanced_image, img_lowlight))

			loss_col = 5*torch.mean(L_color(enhanced_image))

			loss_exp = 10*torch.mean(L_exp(enhanced_image))

			loss_local = L_local(enhanced_image, highlight_mask)
			
			
			# best_loss
			loss =  Loss_TV + loss_spa + loss_col + loss_exp #/+ loss_local
			#


			optimizer.zero_grad()
			loss.backward()
			torch.nn.utils.clip_grad_norm(DCE_net.parameters(),config.grad_clip_norm)
			optimizer.step()

			# 累积损失用于epoch统计
			epoch_loss += loss.item()
			epoch_loss_tv += Loss_TV.item()
			epoch_loss_spa += loss_spa.item()
			epoch_loss_col += loss_col.item()
			epoch_loss_exp += loss_exp.item()

			if ((iteration+1) % config.display_iter) == 0:
				current_time = datetime.now().strftime("%H:%M:%S")
				progress = (iteration + 1) / total_batches * 100
				print(f"   [{current_time}] Batch [{iteration+1:4d}/{total_batches}] ({progress:5.1f}%) | "
				      f"Loss: {loss.item():.6f} | "
				      f"TV: {Loss_TV.item():.4f} | "
				      f"Spa: {loss_spa.item():.4f} | "
				      f"Col: {loss_col.item():.4f} | "
				      f"Exp: {loss_exp.item():.4f}")

			if ((iteration+1) % config.snapshot_iter) == 0:
				torch.save(DCE_net.state_dict(), config.snapshots_folder + "Epoch" + str(epoch) + '.pth')

		# Epoch结束统计
		epoch_end_time = time.time()
		epoch_duration = epoch_end_time - epoch_start_time
		avg_loss = epoch_loss / total_batches
		avg_loss_tv = epoch_loss_tv / total_batches
		avg_loss_spa = epoch_loss_spa / total_batches
		avg_loss_col = epoch_loss_col / total_batches
		avg_loss_exp = epoch_loss_exp / total_batches

		print(f"\n✅ Epoch [{epoch+1}/{config.num_epochs}] 完成!")
		print(f"   ⏱️  用时: {epoch_duration:.1f}秒 ({epoch_duration/60:.1f}分钟)")
		print(f"   📊 平均损失: {avg_loss:.6f}")
		print(f"      - TV Loss: {avg_loss_tv:.6f}")
		print(f"      - Spatial Loss: {avg_loss_spa:.6f}")
		print(f"      - Color Loss: {avg_loss_col:.6f}")
		print(f"      - Exposure Loss: {avg_loss_exp:.6f}")

		# 保存epoch模型
		torch.save(DCE_net.state_dict(), config.snapshots_folder + f"Epoch_{epoch+1:03d}.pth")
		print(f"   💾 模型已保存: {config.snapshots_folder}Epoch_{epoch+1:03d}.pth")
		print("-" * 60)

	# 训练完成总结
	total_training_time = time.time() - training_start_time
	print(f"\n🎉 训练完成!")
	print(f"📊 训练总结:")
	print(f"   - 总训练时间: {total_training_time:.1f}秒 ({total_training_time/60:.1f}分钟)")
	print(f"   - 完成epoch数: {config.num_epochs}")
	print(f"   - 平均每epoch时间: {total_training_time/config.num_epochs:.1f}秒")
	print(f"   - 最终模型: {config.snapshots_folder}Epoch_{config.num_epochs:03d}.pth")
	print("="*60)




if __name__ == "__main__":

	parser = argparse.ArgumentParser()

	# Input Parameters
	parser.add_argument('--lowlight_images_path', type=str, default="../../../train_data/in/")
	parser.add_argument('--lr', type=float, default=0.0001)
	parser.add_argument('--weight_decay', type=float, default=0.0001)
	parser.add_argument('--grad_clip_norm', type=float, default=0.1)
	parser.add_argument('--num_epochs', type=int, default=100)  # 减少到100个epoch，更合理
	parser.add_argument('--train_batch_size', type=int, default=8)
	parser.add_argument('--val_batch_size', type=int, default=4)
	parser.add_argument('--num_workers', type=int, default=4)
	parser.add_argument('--display_iter', type=int, default=5)  # 更频繁显示进度
	parser.add_argument('--snapshot_iter', type=int, default=50)  # 减少保存频率
	parser.add_argument('--snapshots_folder', type=str, default="snapshots/")
	parser.add_argument('--load_pretrain', type=bool, default= False)
	parser.add_argument('--pretrain_dir', type=str, default= "snapshots/Epoch99.pth")

	config = parser.parse_args()

	if not os.path.exists(config.snapshots_folder):
		os.mkdir(config.snapshots_folder)


	train(config)








	
