#!/usr/bin/env python3
"""
为Zero-DCE准备测试数据
从训练数据中分离出一部分作为测试集
"""

import os
import shutil
import glob
import random
from pathlib import Path

def prepare_zerodce_test_data():
    """为Zero-DCE准备测试数据"""
    
    print("🔧 为Zero-DCE准备测试数据...")
    
    # 源数据路径
    source_path = "../../../train_data/in"
    
    # 目标路径
    test_data_path = "test_data"
    
    # 创建测试数据目录
    os.makedirs(test_data_path, exist_ok=True)
    print(f"创建测试目录: {test_data_path}")
    
    # 获取所有低光照图像
    image_files = []
    for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff']:
        image_files.extend(glob.glob(os.path.join(source_path, ext)))
        image_files.extend(glob.glob(os.path.join(source_path, ext.upper())))
    
    image_files.sort()
    print(f"找到 {len(image_files)} 张低光照图像")
    
    # 随机选择200张图像作为测试集
    random.seed(42)
    random.shuffle(image_files)
    
    test_count = min(200, len(image_files))
    test_files = image_files[:test_count]
    
    print(f"选择 {test_count} 张图像作为测试集")
    
    # 复制测试图像
    for i, src_path in enumerate(test_files):
        # 获取文件扩展名
        file_ext = os.path.splitext(src_path)[1]
        
        # 使用统一命名
        dst_name = f"test_{i+1:03d}{file_ext}"
        dst_path = os.path.join(test_data_path, dst_name)
        
        # 复制文件
        shutil.copy2(src_path, dst_path)
        
        if (i + 1) % 50 == 0:
            print(f"已复制 {i+1}/{test_count} 张测试图像")
    
    print(f"\n✅ 测试数据准备完成!")
    print(f"📁 测试图像位置: {test_data_path}/")
    print(f"📊 测试图像数量: {test_count}")
    
    # 创建测试脚本
    create_test_script(test_data_path)

def create_test_script(test_data_path):
    """创建测试脚本"""
    
    test_script = f"""#!/usr/bin/env python3
'''
Zero-DCE测试脚本
'''

import torch
import torch.nn as nn
import torchvision
import torch.backends.cudnn as cudnn
import torch.optim
import os
import sys
import argparse
import time
import numpy as np
import cv2
from PIL import Image
import glob

import model

def lowlight_test(image_path, model_path, output_dir):
    \"\"\"测试单张图像\"\"\"
    
    # 加载模型
    DCE_net = model.enhance_net_nopool()
    DCE_net.load_state_dict(torch.load(model_path, map_location='cpu'))
    DCE_net.eval()
    
    if torch.cuda.is_available():
        DCE_net = DCE_net.cuda()
    
    # 读取图像
    data_lowlight = Image.open(image_path)
    data_lowlight = (np.asarray(data_lowlight)/255.0)
    data_lowlight = torch.from_numpy(data_lowlight).float()
    data_lowlight = data_lowlight.permute(2,0,1)
    data_lowlight = data_lowlight.unsqueeze(0)
    
    if torch.cuda.is_available():
        data_lowlight = data_lowlight.cuda()
    
    # 增强
    with torch.no_grad():
        enhanced_image, _, _ = DCE_net(data_lowlight)
    
    # 保存结果
    result = enhanced_image.squeeze(0).permute(1,2,0).cpu().numpy()
    result = np.clip(result, 0, 1)
    result = (result * 255).astype(np.uint8)
    
    # 输出文件名
    input_name = os.path.splitext(os.path.basename(image_path))[0]
    output_path = os.path.join(output_dir, f"{{input_name}}_enhanced.png")
    
    # 保存
    cv2.imwrite(output_path, cv2.cvtColor(result, cv2.COLOR_RGB2BGR))
    
    return output_path

def test_all_images():
    \"\"\"测试所有图像\"\"\"
    
    # 参数设置
    test_data_dir = "{test_data_path}"
    model_path = "snapshots/Epoch_100.pth"  # 修改为您的模型路径
    output_dir = "test_results"
    
    # 检查模型文件
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {{model_path}}")
        print("请先训练模型或指定正确的模型路径")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有测试图像
    test_images = []
    for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp']:
        test_images.extend(glob.glob(os.path.join(test_data_dir, ext)))
    
    test_images.sort()
    
    if len(test_images) == 0:
        print(f"❌ 在 {{test_data_dir}} 中没有找到测试图像")
        return
    
    print(f"🔍 找到 {{len(test_images)}} 张测试图像")
    print(f"📁 输出目录: {{output_dir}}")
    print("="*50)
    
    # 测试所有图像
    start_time = time.time()
    
    for i, image_path in enumerate(test_images):
        print(f"处理 [{{i+1}}/{{len(test_images)}}]: {{os.path.basename(image_path)}}")
        
        try:
            output_path = lowlight_test(image_path, model_path, output_dir)
            print(f"  ✅ 保存到: {{os.path.basename(output_path)}}")
        except Exception as e:
            print(f"  ❌ 处理失败: {{e}}")
    
    end_time = time.time()
    print("="*50)
    print(f"🎉 测试完成!")
    print(f"⏱️  总用时: {{end_time - start_time:.2f}}秒")
    print(f"📊 处理图像: {{len(test_images)}}张")
    print(f"📁 结果保存在: {{output_dir}}/")

if __name__ == "__main__":
    test_all_images()
"""
    
    # 保存测试脚本
    script_path = "test_zerodce.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"📝 已创建测试脚本: {script_path}")

if __name__ == "__main__":
    prepare_zerodce_test_data()
