#!/usr/bin/env python3
"""
测试Zero-DCE训练显示功能
运行几个iteration来验证epoch信息打印是否正常
"""

import torch
import torch.nn as nn
import torchvision
import torch.backends.cudnn as cudnn
import torch.optim
import os
import sys
import argparse
import time
import dataloader
import model
import Myloss
import numpy as np
import cv2
from datetime import datetime

def weights_init(m):
    classname = m.__class__.__name__
    if classname.find('Conv') != -1:
        m.weight.data.normal_(0.0, 0.02)
    elif classname.find('BatchNorm') != -1:
        m.weight.data.normal_(1.0, 0.02)
        m.bias.data.fill_(0)

def create_triangle_mask(image_size, triangle_size):
    height, width = image_size
    mask = np.zeros((height, width), dtype=np.float32)
    
    # 定义三角形的三个顶点
    apex = (width // 2, height // 4)  # 顶点
    base_left = (width // 2 - triangle_size, height // 2)  # 左下角
    base_right = (width // 2 + triangle_size, height // 2)  # 右下角
    
    # 使用 OpenCV 的 fillConvexPoly 函数填充三角形区域
    triangle_points = np.array([apex, base_left, base_right], dtype=np.int32)
    cv2.fillConvexPoly(mask, triangle_points, 1.0)
    
    return torch.from_numpy(mask).cuda()

def test_train_display():
    """测试训练显示功能，只运行几个batch"""
    
    print("🧪 测试Zero-DCE训练显示功能...")
    
    # 检查数据路径
    data_path = "../../../train_data/in/"
    if not os.path.exists(data_path):
        print(f"❌ 数据路径不存在: {data_path}")
        print("请确保训练数据在正确位置")
        return
    
    # 设置设备
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，请检查GPU设置")
        return
    
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'
    
    # 初始化模型
    DCE_net = model.enhance_net_nopool().cuda()
    DCE_net.apply(weights_init)
    
    # 加载数据
    train_dataset = dataloader.lowlight_loader(data_path)
    train_loader = torch.utils.data.DataLoader(
        train_dataset, 
        batch_size=2,  # 小批次用于测试
        shuffle=True, 
        num_workers=0,  # 避免多进程问题
        pin_memory=True
    )
    
    if len(train_loader) == 0:
        print("❌ 没有找到训练数据")
        return
    
    print(f"✅ 找到 {len(train_dataset)} 张训练图像")
    print(f"✅ 批次数: {len(train_loader)}")
    
    # 初始化损失函数
    L_color = Myloss.L_color()
    L_spa = Myloss.L_spa()
    L_exp = Myloss.L_exp(16, 0.6)
    L_TV = Myloss.L_TV()
    L_local = Myloss.L_local_highlight(target_brightness_high=0.7, target_brightness_low=0.3)
    
    # 初始化优化器
    optimizer = torch.optim.Adam(DCE_net.parameters(), lr=0.0001, weight_decay=0.0001)
    
    DCE_net.train()
    
    # 模拟训练几个epoch
    num_test_epochs = 2
    total_batches = min(5, len(train_loader))  # 最多测试5个batch
    
    print(f"\n🚀 开始测试训练显示 (测试 {num_test_epochs} 个epoch，每epoch最多 {total_batches} 个batch)")
    print("="*60)
    
    training_start_time = time.time()
    
    for epoch in range(num_test_epochs):
        epoch_start_time = time.time()
        epoch_loss = 0.0
        epoch_loss_tv = 0.0
        epoch_loss_spa = 0.0
        epoch_loss_col = 0.0
        epoch_loss_exp = 0.0
        
        print(f"\n📈 Epoch [{epoch+1}/{num_test_epochs}] 开始训练...")
        
        for iteration, img_lowlight in enumerate(train_loader):
            if iteration >= total_batches:  # 限制batch数量
                break
                
            highlight_mask = create_triangle_mask(img_lowlight.shape[2:], 100)
            img_lowlight = img_lowlight.cuda()
            
            enhanced_image_1, enhanced_image, A = DCE_net(img_lowlight)
            
            Loss_TV = 200 * L_TV(A)
            loss_spa = torch.mean(L_spa(enhanced_image, img_lowlight))
            loss_col = 5 * torch.mean(L_color(enhanced_image))
            loss_exp = 10 * torch.mean(L_exp(enhanced_image))
            loss_local = L_local(enhanced_image, highlight_mask)
            
            loss = Loss_TV + loss_spa + loss_col + loss_exp
            
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm(DCE_net.parameters(), 0.1)
            optimizer.step()
            
            # 累积损失
            epoch_loss += loss.item()
            epoch_loss_tv += Loss_TV.item()
            epoch_loss_spa += loss_spa.item()
            epoch_loss_col += loss_col.item()
            epoch_loss_exp += loss_exp.item()
            
            # 显示进度 (每个batch都显示，用于测试)
            current_time = datetime.now().strftime("%H:%M:%S")
            progress = (iteration + 1) / total_batches * 100
            print(f"   [{current_time}] Batch [{iteration+1:4d}/{total_batches}] ({progress:5.1f}%) | "
                  f"Loss: {loss.item():.6f} | "
                  f"TV: {Loss_TV.item():.4f} | "
                  f"Spa: {loss_spa.item():.4f} | "
                  f"Col: {loss_col.item():.4f} | "
                  f"Exp: {loss_exp.item():.4f}")
        
        # Epoch结束统计
        epoch_end_time = time.time()
        epoch_duration = epoch_end_time - epoch_start_time
        avg_loss = epoch_loss / total_batches
        avg_loss_tv = epoch_loss_tv / total_batches
        avg_loss_spa = epoch_loss_spa / total_batches
        avg_loss_col = epoch_loss_col / total_batches
        avg_loss_exp = epoch_loss_exp / total_batches
        
        print(f"\n✅ Epoch [{epoch+1}/{num_test_epochs}] 完成!")
        print(f"   ⏱️  用时: {epoch_duration:.1f}秒")
        print(f"   📊 平均损失: {avg_loss:.6f}")
        print(f"      - TV Loss: {avg_loss_tv:.6f}")
        print(f"      - Spatial Loss: {avg_loss_spa:.6f}")
        print(f"      - Color Loss: {avg_loss_col:.6f}")
        print(f"      - Exposure Loss: {avg_loss_exp:.6f}")
        print("-" * 60)
    
    # 测试完成总结
    total_training_time = time.time() - training_start_time
    print(f"\n🎉 测试完成!")
    print(f"📊 测试总结:")
    print(f"   - 总测试时间: {total_training_time:.1f}秒")
    print(f"   - 完成epoch数: {num_test_epochs}")
    print(f"   - 平均每epoch时间: {total_training_time/num_test_epochs:.1f}秒")
    print("="*60)
    print("✅ 训练显示功能测试通过！现在可以运行完整训练了。")

if __name__ == "__main__":
    test_train_display()
