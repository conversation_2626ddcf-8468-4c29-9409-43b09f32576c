#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大学综合得分预测
使用线性回归模型根据大学各项指标排名预测综合得分

"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_and_explore_data():
    """加载并探索数据"""
    print("=" * 50)
    print("1. 数据加载与探索")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('cwurData.csv')
    print(f"数据集形状: {df.shape}")
    print(f"数据集列名: {list(df.columns)}")
    
    # 显示基本信息
    print("\n数据集基本信息:")
    print(df.info())
    
    print("\n数据集前5行:")
    print(df.head())
    
    print("\n数据集统计信息:")
    print(df.describe())
    
    # 检查缺失值
    print("\n缺失值统计:")
    missing_values = df.isnull().sum()
    print(missing_values[missing_values > 0])
    
    return df

def visualize_data(df):
    """数据可视化"""
    print("\n" + "=" * 50)
    print("2. 数据可视化分析")
    print("=" * 50)
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 得分分布
    axes[0, 0].hist(df['score'].dropna(), bins=30, alpha=0.7, color='skyblue')
    axes[0, 0].set_title('综合得分分布')
    axes[0, 0].set_xlabel('得分')
    axes[0, 0].set_ylabel('频次')
    
    # 2. 地区分布
    region_counts = df['region'].value_counts().head(10)
    axes[0, 1].bar(range(len(region_counts)), region_counts.values, color='lightcoral')
    axes[0, 1].set_title('前10个地区的大学数量')
    axes[0, 1].set_xlabel('地区')
    axes[0, 1].set_ylabel('大学数量')
    axes[0, 1].set_xticks(range(len(region_counts)))
    axes[0, 1].set_xticklabels(region_counts.index, rotation=45, ha='right')
    
    # 3. 年份分布
    year_counts = df['year'].value_counts().sort_index()
    axes[1, 0].plot(year_counts.index, year_counts.values, marker='o', color='green')
    axes[1, 0].set_title('各年份数据量')
    axes[1, 0].set_xlabel('年份')
    axes[1, 0].set_ylabel('数据量')
    
    # 4. 得分与排名的关系
    axes[1, 1].scatter(df['world_rank'], df['score'], alpha=0.6, color='purple')
    axes[1, 1].set_title('世界排名与综合得分关系')
    axes[1, 1].set_xlabel('世界排名')
    axes[1, 1].set_ylabel('综合得分')
    
    plt.tight_layout()
    plt.savefig('data_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 特征相关性热力图
    feature_cols = ['quality_of_education', 'alumni_employment', 'quality_of_faculty',
                   'publications', 'influence', 'citations', 'broad_impact', 'patents', 'score']
    
    # 只选择有数据的行
    corr_data = df[feature_cols].dropna()
    
    plt.figure(figsize=(10, 8))
    correlation_matrix = corr_data.corr()
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, fmt='.2f')
    plt.title('特征相关性热力图')
    plt.tight_layout()
    plt.savefig('correlation_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return correlation_matrix

def prepare_data(df):
    """数据预处理"""
    print("\n" + "=" * 50)
    print("3. 数据预处理")
    print("=" * 50)
    
    # 定义特征列
    feature_cols = ['quality_of_education', 'alumni_employment', 'quality_of_faculty',
                   'publications', 'influence', 'citations', 'broad_impact', 'patents']
    target_col = 'score'
    
    # 选择有完整数据的行
    complete_data = df[feature_cols + [target_col]].dropna()
    print(f"完整数据行数: {len(complete_data)}")
    
    X = complete_data[feature_cols]
    y = complete_data[target_col]
    
    print(f"特征矩阵形状: {X.shape}")
    print(f"目标变量形状: {y.shape}")
    
    return X, y, complete_data

def train_linear_regression(X, y):
    """训练线性回归模型"""
    print("\n" + "=" * 50)
    print("4. 线性回归模型训练")
    print("=" * 50)
    # 划分训练集和测试集 (8:2)
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    print(f"训练集大小: {X_train.shape}")
    print(f"测试集大小: {X_test.shape}")
    # 训练线性回归模型
    lr_model = LinearRegression()
    lr_model.fit(X_train, y_train)
    
    # 预测
    y_train_pred = lr_model.predict(X_train)
    y_test_pred = lr_model.predict(X_test)
    # 计算RMSE
    train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
    test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
    # 计算R²
    train_r2 = r2_score(y_train, y_train_pred)
    test_r2 = r2_score(y_test, y_test_pred)
    
    print(f"\n线性回归模型结果:")
    print(f"训练集 RMSE: {train_rmse:.4f}")
    print(f"测试集 RMSE: {test_rmse:.4f}")
    print(f"训练集 R²: {train_r2:.4f}")
    print(f"测试集 R²: {test_r2:.4f}")
    
    # 分析系数
    print(f"\n模型系数分析:")
    print(f"截距: {lr_model.intercept_:.4f}")
    
    feature_importance = pd.DataFrame({
        '特征': X.columns,
        '系数': lr_model.coef_,
        '绝对值': np.abs(lr_model.coef_)
    }).sort_values('绝对值', ascending=False)
    
    print("\n特征系数 (按绝对值排序):")
    print(feature_importance)
    
    return lr_model, X_train, X_test, y_train, y_test, y_test_pred, test_rmse

def compare_models(X_train, X_test, y_train, y_test):
    """比较不同回归模型"""
    print("\n" + "=" * 50)
    print("5. 不同回归模型对比")
    print("=" * 50)
    models = {
        '线性回归': LinearRegression(),
        '岭回归': Ridge(alpha=1.0),
        'Lasso回归': Lasso(alpha=1.0),
        '随机森林': RandomForestRegressor(n_estimators=100, random_state=42)
    }
    results = []
    for name, model in models.items():
        # 训练模型
        model.fit(X_train, y_train)
        # 预测
        y_pred = model.predict(X_test)
        # 计算指标
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        r2 = r2_score(y_test, y_pred)
        
        results.append({
            '模型': name,
            'RMSE': rmse,
            'R²': r2
        })
        print(f"{name}: RMSE={rmse:.4f}, R²={r2:.4f}")
    # 创建结果对比图
    results_df = pd.DataFrame(results)
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # RMSE对比
    ax1.bar(results_df['模型'], results_df['RMSE'], color='lightblue')
    ax1.set_title('不同模型RMSE对比')
    ax1.set_ylabel('RMSE')
    ax1.tick_params(axis='x', rotation=45)
    # R²对比
    ax2.bar(results_df['模型'], results_df['R²'], color='lightgreen')
    ax2.set_title('不同模型R²对比')
    ax2.set_ylabel('R²')
    ax2.tick_params(axis='x', rotation=45)
    plt.tight_layout()
    plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return results_df

def add_region_features(df, X, y):
    """添加地区特征到线性回归模型"""
    print("\n" + "=" * 50)
    print("6. 融入地区特征的线性回归")
    print("=" * 50)
    
    # 获取完整数据
    feature_cols = ['quality_of_education', 'alumni_employment', 'quality_of_faculty',
                   'publications', 'influence', 'citations', 'broad_impact', 'patents']
    
    complete_data = df[feature_cols + ['score', 'region']].dropna()
    
    # 对地区进行独热编码
    region_dummies = pd.get_dummies(complete_data['region'], prefix='region')
    # 合并特征
    X_with_region = pd.concat([
        complete_data[feature_cols].reset_index(drop=True),
        region_dummies.reset_index(drop=True)
    ], axis=1)
    
    y_with_region = complete_data['score'].reset_index(drop=True)
    print(f"添加地区特征后的特征数量: {X_with_region.shape[1]}")
    print(f"地区特征数量: {region_dummies.shape[1]}")
    
    # 划分训练集和测试集
    X_train_region, X_test_region, y_train_region, y_test_region = train_test_split(
        X_with_region, y_with_region, test_size=0.2, random_state=42
    )
    # 训练模型
    lr_region_model = LinearRegression()
    lr_region_model.fit(X_train_region, y_train_region)
    # 预测
    y_pred_region = lr_region_model.predict(X_test_region)
    
    # 计算指标
    rmse_region = np.sqrt(mean_squared_error(y_test_region, y_pred_region))
    r2_region = r2_score(y_test_region, y_pred_region)
    
    print(f"\n融入地区特征的线性回归结果:")
    print(f"测试集 RMSE: {rmse_region:.4f}")
    print(f"测试集 R²: {r2_region:.4f}")
    
    return rmse_region, r2_region

def main():
    """主函数"""
    print("大学综合得分预测分析")
    print("=" * 60)
    
    # 1. 加载和探索数据
    df = load_and_explore_data()
    
    # 2. 数据可视化
    correlation_matrix = visualize_data(df)
    
    # 3. 数据预处理
    X, y, complete_data = prepare_data(df)
    
    # 4. 训练线性回归模型
    lr_model, X_train, X_test, y_train, y_test, y_test_pred, test_rmse = train_linear_regression(X, y)
    
    # 5. 比较不同模型
    results_df = compare_models(X_train, X_test, y_train, y_test)
    
    # 6. 融入地区特征
    rmse_region, r2_region = add_region_features(df, X, y)
    
    # 7. 最终结果总结
    print("\n" + "=" * 50)
    print("7. 最终结果总结")
    print("=" * 50)
    
    print(f"基础线性回归模型测试集RMSE: {test_rmse:.4f}")
    print(f"融入地区特征后测试集RMSE: {rmse_region:.4f}")
    print(f"RMSE改善: {test_rmse - rmse_region:.4f}")
    
    print("\n模型性能对比:")
    print(results_df.to_string(index=False))
    
    # 预测vs实际值散点图
    plt.figure(figsize=(10, 6))
    plt.scatter(y_test, y_test_pred, alpha=0.6)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    plt.xlabel('实际得分')
    plt.ylabel('预测得分')
    plt.title('线性回归模型: 预测值 vs 实际值')
    plt.tight_layout()
    plt.savefig('prediction_vs_actual.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n分析完成！所有图表已保存为PNG文件。")

if __name__ == "__main__":
    main()
