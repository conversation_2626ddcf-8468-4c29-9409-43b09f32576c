# Ultimate Tic-Tac-Toe MCTS 使用说明

## 快速开始

### 1. 基本测试
```bash
# 快速验证功能
python quick_test.py
```

### 2. 交互式游戏
```bash
# 人类 vs MCTS
python p2_play.py human mcts_vanilla
python p2_play.py human mcts_modified

# 两个MCTS对战
python p2_play.py mcts_vanilla mcts_modified
```

### 3. 自动对战模拟
```bash
# 简单模拟
python p2_sim.py mcts_vanilla random_bot
python p2_sim.py mcts_modified mcts_vanilla
```

## 完整实验

### 实验1：树大小影响测试
```bash
python experiment1.py
```
- **目的**: 测试不同树大小对性能的影响
- **配置**: Player1固定100节点，Player2测试50,100,200,500,1000节点
- **时间**: 约30-60分钟
- **输出**: `experiment1_results.png` 和 `experiment1_data.txt`

### 实验2：算法版本对比
```bash
python experiment2.py
```
- **目的**: 比较vanilla和modified版本性能
- **配置**: 两版本在500,1000,1500节点下对战
- **时间**: 约60-120分钟
- **输出**: `experiment2_results.png` 和 `experiment2_data.txt`

## 性能优化

### 多线程加速
- 实验脚本自动使用多线程加速
- 根据CPU核心数自动调整线程数
- 大幅提升运行速度

### 调整参数
如需调整树大小，编辑对应文件：
```python
# 在 mcts_vanilla.py 或 mcts_modified.py 中
num_nodes = 100  # 修改这个值
```

## 算法版本

### Vanilla MCTS
- 标准四阶段MCTS算法
- 随机rollout策略
- 基础UCB选择

### Modified MCTS  
- 智能rollout（80%启发式 + 20%随机）
- 改进UCB公式（位置启发式奖励）
- 多因素动作评估
- 更稳健的最终决策

## 文件说明

### 核心文件
- `mcts_vanilla.py` - 标准MCTS实现
- `mcts_modified.py` - 改进MCTS实现
- `p2_t3.py` - 游戏逻辑
- `mcts_node.py` - MCTS节点类

### 实验文件
- `experiment1.py` - 树大小影响实验
- `experiment2.py` - 算法对比实验
- `quick_test.py` - 快速功能测试

### 游戏文件
- `p2_play.py` - 交互式游戏
- `p2_sim.py` - 自动对战模拟
- `random_bot.py` - 随机策略
- `rollout_bot.py` - Rollout策略

## 注意事项

1. **运行时间**: 大规模实验需要较长时间，建议在性能较好的机器上运行
2. **内存使用**: 大树大小会消耗更多内存
3. **随机性**: 由于游戏的随机性，结果可能有一定波动
4. **中断恢复**: 如果实验中断，需要重新运行

## 故障排除

### 常见问题
1. **Python版本**: 需要Python 3.7+
2. **依赖包**: 确保安装了matplotlib和numpy
3. **文件路径**: 确保在正确的目录下运行

### 安装依赖
```bash
pip install matplotlib numpy
```

### 性能调优
- 减少游戏数量以加快测试速度
- 调整树大小平衡性能和速度
- 使用快速测试验证功能

## 结果解读

### 实验1结果
- 横轴：Player2的树大小
- 纵轴：Player2的胜率
- 预期：随树大小增加胜率提升

### 实验2结果
- 比较Modified和Vanilla版本
- 显示不同树大小下的性能差异
- 评估改进效果

## 扩展使用

### 自定义参数
可以修改以下参数：
- 树大小 (`num_nodes`)
- 探索因子 (`explore_faction`)
- 启发式权重 (`CENTER_WEIGHT`, `BLOCK_WEIGHT`, `WIN_WEIGHT`)

### 添加新策略
参考现有实现添加新的MCTS变体或其他游戏AI策略。
