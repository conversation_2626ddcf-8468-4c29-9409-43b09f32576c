#!/usr/bin/env python3
"""
实验2：比较vanilla和modified版本在相同树大小下的性能
测试3种不同的树大小：500, 1000, 1500节点
"""

import sys
import os
from timeit import default_timer as time
import matplotlib.pyplot as plt
import numpy as np

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import p2_t3
import mcts_vanilla
import mcts_modified

def run_games(player1_func, player2_func, num_games=100, description=""):
    """运行指定数量的游戏并返回结果统计"""
    board = p2_t3.Board()
    state0 = board.starting_state()
    
    wins = {'draw': 0, 1: 0, 2: 0}
    
    print(f"  运行 {description}")
    
    for i in range(num_games):
        if i % 20 == 0:
            print(f"    游戏 {i+1}/{num_games}")
        
        state = state0
        current_player = player1_func
        
        while not board.is_ended(state):
            last_action = current_player(board, state)
            state = board.next_state(state, last_action)
            current_player = player1_func if current_player == player2_func else player2_func
        
        final_score = board.points_values(state)
        winner = 'draw'
        if final_score[1] == 1:
            winner = 1
        elif final_score[2] == 1:
            winner = 2
        
        wins[winner] = wins.get(winner, 0) + 1
    
    return wins

def set_tree_size(size):
    """设置两个MCTS版本的树大小"""
    mcts_vanilla.num_nodes = size
    mcts_modified.num_nodes = size

def experiment2():
    """实验2主函数"""
    print("开始实验2：比较vanilla和modified版本的性能")
    print("=" * 60)
    
    # 测试的树大小
    tree_sizes = [500, 1000, 1500]
    num_games = 100
    
    all_results = []
    
    for tree_size in tree_sizes:
        print(f"\n测试树大小: {tree_size} 节点")
        print("-" * 40)
        
        # 设置树大小
        set_tree_size(tree_size)
        
        # 测试1: Modified vs Vanilla (Modified为Player 1)
        print("测试1: Modified (Player 1) vs Vanilla (Player 2)")
        start_time = time()
        wins1 = run_games(mcts_modified.think, mcts_vanilla.think, num_games, 
                         "Modified vs Vanilla")
        time1 = time() - start_time
        
        # 测试2: Vanilla vs Modified (Vanilla为Player 1)
        print("测试2: Vanilla (Player 1) vs Modified (Player 2)")
        start_time = time()
        wins2 = run_games(mcts_vanilla.think, mcts_modified.think, num_games,
                         "Vanilla vs Modified")
        time2 = time() - start_time
        
        # 计算Modified的总体表现
        modified_wins = wins1[1] + wins2[2]  # Modified作为P1的胜利 + Modified作为P2的胜利
        total_games = num_games * 2
        modified_win_rate = modified_wins / total_games
        
        result = {
            'tree_size': tree_size,
            'modified_wins': modified_wins,
            'modified_win_rate': modified_win_rate,
            'vanilla_wins': total_games - modified_wins - (wins1['draw'] + wins2['draw']),
            'draws': wins1['draw'] + wins2['draw'],
            'total_games': total_games,
            'time_elapsed': time1 + time2,
            'wins1': wins1,  # Modified vs Vanilla
            'wins2': wins2   # Vanilla vs Modified
        }
        
        all_results.append(result)
        
        print(f"结果汇总:")
        print(f"  Modified总胜利: {modified_wins}/{total_games} ({modified_win_rate:.2%})")
        print(f"  平局: {result['draws']}")
        print(f"  总用时: {result['time_elapsed']:.2f} 秒")
    
    # 生成图表
    generate_plots(all_results)
    
    # 保存结果
    save_results(all_results)
    
    return all_results

def generate_plots(results):
    """生成实验结果图表"""
    tree_sizes = [r['tree_size'] for r in results]
    modified_rates = [r['modified_win_rate'] for r in results]
    vanilla_rates = [(r['total_games'] - r['modified_wins'] - r['draws']) / r['total_games'] 
                    for r in results]
    draw_rates = [r['draws'] / r['total_games'] for r in results]
    
    # 创建柱状图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 图1: 胜率比较
    x = np.arange(len(tree_sizes))
    width = 0.35
    
    ax1.bar(x - width/2, modified_rates, width, label='Modified MCTS', color='blue', alpha=0.7)
    ax1.bar(x + width/2, vanilla_rates, width, label='Vanilla MCTS', color='red', alpha=0.7)
    
    ax1.set_xlabel('树大小 (节点数)')
    ax1.set_ylabel('胜率')
    ax1.set_title('实验2: Modified vs Vanilla MCTS 胜率比较')
    ax1.set_xticks(x)
    ax1.set_xticklabels(tree_sizes)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 1)
    
    # 添加数值标签
    for i, (mod_rate, van_rate) in enumerate(zip(modified_rates, vanilla_rates)):
        ax1.text(i - width/2, mod_rate + 0.01, f'{mod_rate:.2%}', 
                ha='center', va='bottom')
        ax1.text(i + width/2, van_rate + 0.01, f'{van_rate:.2%}', 
                ha='center', va='bottom')
    
    # 图2: 详细结果堆叠图
    ax2.bar(tree_sizes, modified_rates, label='Modified胜利', color='blue', alpha=0.7)
    ax2.bar(tree_sizes, vanilla_rates, bottom=modified_rates, 
           label='Vanilla胜利', color='red', alpha=0.7)
    ax2.bar(tree_sizes, draw_rates, 
           bottom=[m+v for m,v in zip(modified_rates, vanilla_rates)],
           label='平局', color='gray', alpha=0.7)
    
    ax2.set_xlabel('树大小 (节点数)')
    ax2.set_ylabel('比例')
    ax2.set_title('实验2: 详细结果分布')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 1)
    
    plt.tight_layout()
    plt.savefig('2808092173544445255/experiment2_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n图表已保存为: experiment2_results.png")

def save_results(results):
    """保存实验结果到文件"""
    with open('2808092173544445255/experiment2_data.txt', 'w', encoding='utf-8') as f:
        f.write("实验2结果：Modified vs Vanilla MCTS性能比较\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("总体结果:\n")
        f.write("树大小\tModified胜率\tVanilla胜率\t平局率\t总游戏数\t用时(秒)\n")
        f.write("-" * 70 + "\n")
        
        for r in results:
            vanilla_rate = (r['total_games'] - r['modified_wins'] - r['draws']) / r['total_games']
            draw_rate = r['draws'] / r['total_games']
            f.write(f"{r['tree_size']}\t{r['modified_win_rate']:.2%}\t{vanilla_rate:.2%}\t"
                   f"{draw_rate:.2%}\t{r['total_games']}\t{r['time_elapsed']:.2f}\n")
        
        f.write("\n详细结果:\n")
        f.write("-" * 70 + "\n")
        
        for r in results:
            f.write(f"\n树大小 {r['tree_size']} 节点:\n")
            f.write(f"  Modified vs Vanilla: {r['wins1']}\n")
            f.write(f"  Vanilla vs Modified: {r['wins2']}\n")
    
    print(f"详细结果已保存为: experiment2_data.txt")

if __name__ == "__main__":
    results = experiment2()
    
    print("\n" + "=" * 60)
    print("实验2完成！")
    print("主要发现:")
    
    # 分析结果
    for r in results:
        improvement = r['modified_win_rate'] - 0.5  # 相对于50%的改进
        print(f"- {r['tree_size']}节点: Modified胜率 {r['modified_win_rate']:.2%} "
              f"(相对随机提升 {improvement:+.2%})")
    
    # 计算平均性能
    avg_modified_rate = np.mean([r['modified_win_rate'] for r in results])
    print(f"- Modified平均胜率: {avg_modified_rate:.2%}")
    
    if avg_modified_rate > 0.5:
        print("- 结论: Modified版本总体上优于Vanilla版本")
    else:
        print("- 结论: Vanilla版本总体上优于Modified版本")
