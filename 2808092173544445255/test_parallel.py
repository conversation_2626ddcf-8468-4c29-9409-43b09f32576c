#!/usr/bin/env python3
"""
测试并行版本的实验脚本
"""

import sys
import os
from timeit import default_timer as time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import p2_t3
import mcts_vanilla
import mcts_modified

def test_parallel_experiment1():
    """测试并行版本的实验1"""
    print("测试并行版本实验1...")
    
    # 导入实验1模块
    import experiment1
    
    # 设置小参数进行快速测试
    original_tree_sizes = experiment1.tree_sizes if hasattr(experiment1, 'tree_sizes') else [50, 100, 200, 500, 1000]
    
    # 临时修改参数
    mcts_vanilla.num_nodes = 20
    
    # 运行一个小测试
    print("运行快速测试...")
    start_time = time()
    
    wins = experiment1.run_games(mcts_vanilla.think, mcts_vanilla.think, 10)
    
    end_time = time()
    
    print(f"测试完成，用时: {end_time - start_time:.2f} 秒")
    print(f"结果: {wins}")
    
    return True

def test_parallel_experiment2():
    """测试并行版本的实验2"""
    print("\n测试并行版本实验2...")
    
    # 导入实验2模块
    import experiment2
    
    # 设置小参数进行快速测试
    mcts_vanilla.num_nodes = 20
    mcts_modified.num_nodes = 20
    
    # 运行一个小测试
    print("运行快速测试...")
    start_time = time()
    
    wins = experiment2.run_games(mcts_vanilla.think, mcts_modified.think, 10, "测试")
    
    end_time = time()
    
    print(f"测试完成，用时: {end_time - start_time:.2f} 秒")
    print(f"结果: {wins}")
    
    return True

def main():
    """主测试函数"""
    print("并行实验脚本测试")
    print("=" * 40)
    
    try:
        # 测试实验1
        success1 = test_parallel_experiment1()
        
        # 测试实验2
        success2 = test_parallel_experiment2()
        
        if success1 and success2:
            print("\n✅ 所有并行测试通过！")
            print("\n现在可以运行完整实验:")
            print("  python experiment1.py  # 多线程加速版本")
            print("  python experiment2.py  # 多线程加速版本")
            return True
        else:
            print("\n❌ 部分测试失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
