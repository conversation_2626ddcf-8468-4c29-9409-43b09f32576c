# 实验1分析报告：树大小对MCTS性能的影响

## 实验概述

本实验旨在研究不同树大小对Monte Carlo Tree Search (MCTS)算法性能的影响。我们固定Player 1使用100节点的vanilla MCTS，让Player 2使用不同大小的树（50, 100, 200, 500, 1000节点），通过100场游戏的对战结果来评估性能变化。

## 实验设计

### 实验参数
- **游戏类型**: Ultimate Tic-Tac-Toe
- **算法版本**: Vanilla MCTS
- **Player 1树大小**: 固定为100节点
- **Player 2树大小**: 50, 100, 200, 500, 1000节点
- **每组游戏数**: 100场
- **评估指标**: Player 2的胜率

### 实验假设
我们预期随着树大小的增加，MCTS的性能会提升，但收益会逐渐递减。这是因为：
1. 更大的树能够进行更深入的搜索
2. 更多的模拟次数提供更准确的价值估计
3. 但计算资源的边际效用会递减

## 预期结果分析

### 理论预测

基于MCTS算法的理论基础，我们预期看到以下趋势：

1. **50节点 vs 100节点**: Player 2处于劣势，胜率应低于50%
2. **100节点 vs 100节点**: 势均力敌，胜率应接近50%
3. **200节点 vs 100节点**: Player 2开始占优，胜率应超过50%
4. **500节点 vs 100节点**: 明显优势，胜率应显著提升
5. **1000节点 vs 100节点**: 最大优势，但相比500节点的提升可能有限

### 性能曲线特征

预期的性能曲线应该呈现：
- **初期快速增长**: 从50到200节点的胜率提升较为明显
- **中期稳定增长**: 200到500节点继续提升但速度放缓
- **后期收益递减**: 500到1000节点的提升幅度最小

## 影响因素分析

### 算法层面
1. **搜索深度**: 更大的树允许更深的搜索，发现更优策略
2. **统计精度**: 更多模拟提供更可靠的节点价值估计
3. **探索vs利用**: 大树能更好地平衡探索和利用

### 游戏特性
1. **状态空间复杂度**: Ultimate Tic-Tac-Toe的复杂性适中，大树的优势明显
2. **游戏长度**: 较长的游戏让大树的优势更容易体现
3. **战术深度**: 需要前瞻性思考的游戏更能体现大树优势

### 计算资源
1. **时间复杂度**: 树大小与计算时间成正比
2. **内存使用**: 大树需要更多内存存储节点信息
3. **收益递减**: 超过某个阈值后，性能提升不再显著

## 实验意义

### 理论价值
1. **验证MCTS理论**: 确认树大小与性能的正相关关系
2. **找到最优配置**: 识别性价比最高的树大小
3. **理解算法特性**: 深入了解MCTS的行为模式

### 实践指导
1. **资源分配**: 为实际应用选择合适的树大小
2. **性能调优**: 在计算资源和性能之间找到平衡
3. **算法改进**: 为后续优化提供基准数据

## 预期结论

基于理论分析，我们预期得出以下结论：

1. **正相关关系**: 树大小与MCTS性能呈正相关
2. **收益递减**: 性能提升幅度随树大小增加而递减
3. **最优区间**: 存在一个性价比最高的树大小范围
4. **实用建议**: 对于Ultimate Tic-Tac-Toe，200-500节点可能是最佳选择

## 后续研究方向

1. **不同游戏类型**: 在其他游戏上验证结论的普适性
2. **动态调整**: 研究根据游戏状态动态调整树大小
3. **并行化**: 探索并行MCTS对性能的影响
4. **混合策略**: 结合不同树大小的优势

## 实验执行说明

要运行此实验，请执行：
```bash
python experiment1.py
```

实验将自动：
1. 运行所有配置的对战
2. 收集统计数据
3. 生成可视化图表
4. 保存详细结果

预计总运行时间：30-60分钟（取决于硬件性能）

---

