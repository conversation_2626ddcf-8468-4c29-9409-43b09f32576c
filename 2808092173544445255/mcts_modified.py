
from mcts_node import MCTSN<PERSON>
from p2_t3 import Board
from random import choice, random
from math import sqrt, log

num_nodes = 100
explore_faction = 2.

# 启发式权重
CENTER_WEIGHT = 1.5  # 中心位置权重
BLOCK_WEIGHT = 2.0   # 阻止对手获胜权重
WIN_WEIGHT = 3.0     # 获胜动作权重

def traverse_nodes(node: MCTSNode, board: Board, state, bot_identity: int):
    """ Traverses the tree until the end criterion are met.
    e.g. find the best expandable node (node with untried action) if it exist,
    or else a terminal node

    Args:
        node:       A tree node from which the search is traversing.
        board:      The game setup.
        state:      The state of the game.
        identity:   The bot's identity, either 1 or 2

    Returns:
        node: A node from which the next stage of the search can proceed.
        state: The state associated with that node

    """
    # 继续遍历直到找到可扩展的节点或终端节点
    while not board.is_ended(state) and len(node.untried_actions) == 0 and len(node.child_nodes) > 0:
        # 当前玩家是否为对手
        current_player = board.current_player(state)
        is_opponent = (current_player != bot_identity)

        # 使用改进的UCB选择最佳子节点
        best_action = None
        best_ucb_value = float('-inf')

        for action, child_node in node.child_nodes.items():
            ucb_value = ucb_improved(child_node, is_opponent, state, board)
            if ucb_value > best_ucb_value:
                best_ucb_value = ucb_value
                best_action = action

        # 移动到选中的子节点
        node = node.child_nodes[best_action]
        state = board.next_state(state, best_action)

    return node, state

def expand_leaf(node: MCTSNode, board: Board, state):
    """ Adds a new leaf to the tree by creating a new child node for the given node (if it is non-terminal).

    Args:
        node:   The node for which a child will be added.
        board:  The game setup.
        state:  The state of the game.

    Returns:
        node: The added child node
        state: The state associated with that node

    """
    # 如果游戏已结束或没有未尝试的动作，返回当前节点
    if board.is_ended(state) or len(node.untried_actions) == 0:
        return node, state

    # 使用启发式方法选择动作而不是完全随机
    action = select_action_heuristic(node.untried_actions, state, board)

    # 从未尝试列表中移除该动作
    node.untried_actions.remove(action)

    # 计算新状态
    new_state = board.next_state(state, action)

    # 创建新的子节点
    child_node = MCTSNode(parent=node, parent_action=action,
                         action_list=board.legal_actions(new_state))

    # 将子节点添加到父节点的子节点字典中
    node.child_nodes[action] = child_node

    return child_node, new_state


def rollout(board: Board, state):
    """ Given the state of the game, the rollout plays out the remainder using smart heuristics.

    Args:
        board:  The game setup.
        state:  The state of the game.

    Returns:
        state: The terminal game state

    """
    # 智能模拟游戏直到结束
    current_state = state

    while not board.is_ended(current_state):
        # 获取所有合法动作
        legal_actions = board.legal_actions(current_state)

        # 如果没有合法动作，游戏结束
        if not legal_actions:
            break

        # 使用启发式策略选择动作（80%概率）或随机选择（20%概率）
        if random() < 0.8:
            action = select_action_heuristic(legal_actions, current_state, board)
        else:
            action = choice(legal_actions)

        # 执行动作，更新状态
        current_state = board.next_state(current_state, action)

    return current_state


def backpropagate(node, won: bool):
    """ Navigates the tree from a leaf node to the root, updating the win and visit count of each node along the path.

    Args:
        node:   A leaf node.
        won:    An indicator of whether the bot won or lost the game.

    """
    # 从叶节点向根节点回溯，更新每个节点的统计信息
    current_node = node

    while current_node is not None:
        # 增加访问次数
        current_node.visits += 1

        # 如果获胜，增加胜利次数
        if won:
            current_node.wins += 1

        # 移动到父节点
        current_node = current_node.parent

def ucb(node: MCTSNode, is_opponent: bool):
    """ Calcualtes the UCB value for the given node from the perspective of the bot

    Args:
        node:   A node.
        is_opponent: A boolean indicating whether or not the last action was performed by the MCTS bot
    Returns:
        The value of the UCB function for the given node
    """
    # 如果节点从未被访问过，返回无穷大
    if node.visits == 0:
        return float('inf')

    # 计算胜率
    win_rate = node.wins / node.visits

    # 对于对手的回合，需要反转胜率
    if is_opponent:
        win_rate = 1.0 - win_rate

    # 计算探索项
    if node.parent is None or node.parent.visits == 0:
        exploration = 0
    else:
        exploration = explore_faction * sqrt(log(node.parent.visits) / node.visits)

    # UCB值 = 利用项 + 探索项
    return win_rate + exploration

def ucb_improved(node: MCTSNode, is_opponent: bool, state, board: Board):
    """ 改进的UCB函数，加入位置启发式
    """
    base_ucb = ucb(node, is_opponent)

    # 如果是无穷大，直接返回
    if base_ucb == float('inf'):
        return base_ucb

    # 添加位置启发式奖励
    if node.parent_action:
        heuristic_bonus = evaluate_action_heuristic(node.parent_action, state, board) * 0.1
        return base_ucb + heuristic_bonus

    return base_ucb

def select_action_heuristic(actions, state, board: Board):
    """ 使用启发式方法选择动作
    """
    if not actions:
        return None

    # 计算每个动作的启发式分数
    action_scores = []
    for action in actions:
        score = evaluate_action_heuristic(action, state, board)
        action_scores.append((action, score))

    # 按分数排序
    action_scores.sort(key=lambda x: x[1], reverse=True)

    # 使用轮盘赌选择（偏向高分动作）
    total_score = sum(score for _, score in action_scores)
    if total_score <= 0:
        return choice(actions)

    rand_val = random() * total_score
    cumulative = 0
    for action, score in action_scores:
        cumulative += score
        if cumulative >= rand_val:
            return action

    return action_scores[0][0]  # 返回最高分动作

def evaluate_action_heuristic(action, state, board: Board):
    """ 评估动作的启发式分数
    """
    R, C, r, c = action
    score = 1.0  # 基础分数

    # 中心位置奖励
    if r == 1 and c == 1:  # 子棋盘中心
        score += CENTER_WEIGHT
    if R == 1 and C == 1:  # 主棋盘中心
        score += CENTER_WEIGHT * 0.5

    # 检查是否能获胜
    test_state = board.next_state(state, action)
    current_player = board.current_player(state)

    if board.is_ended(test_state):
        outcome = board.points_values(test_state)
        if outcome and outcome[current_player] == 1:
            score += WIN_WEIGHT

    # 检查是否阻止对手获胜
    opponent = 3 - current_player
    # 模拟对手在同一位置下棋
    temp_state = list(state)
    temp_state[-1] = opponent  # 切换到对手
    temp_state = tuple(temp_state)

    if board.is_legal(temp_state, action):
        opponent_test_state = board.next_state(temp_state, action)
        if board.is_ended(opponent_test_state):
            outcome = board.points_values(opponent_test_state)
            if outcome and outcome[opponent] == 1:
                score += BLOCK_WEIGHT

    return score

def get_best_action(root_node: MCTSNode):
    """ Selects the best action from the root node in the MCTS tree

    Args:
        root_node:   The root node
    Returns:
        action: The best action from the root node

    """
    # 如果没有子节点，返回None
    if not root_node.child_nodes:
        return None

    # 结合访问次数和胜率选择最佳动作
    best_action = None
    best_score = float('-inf')

    for action, child_node in root_node.child_nodes.items():
        if child_node.visits == 0:
            continue

        # 计算综合分数：胜率 + 访问次数权重
        win_rate = child_node.wins / child_node.visits
        visit_weight = child_node.visits / root_node.visits
        combined_score = win_rate * 0.7 + visit_weight * 0.3

        if combined_score > best_score:
            best_score = combined_score
            best_action = action

    return best_action

def is_win(board: Board, state, identity_of_bot: int):
    # checks if state is a win state for identity_of_bot
    outcome = board.points_values(state)
    assert outcome is not None, "is_win was called on a non-terminal state"
    return outcome[identity_of_bot] == 1

def think(board: Board, current_state):
    """ Performs MCTS by sampling games and calling the appropriate functions to construct the game tree.

    Args:
        board:  The game setup.
        current_state:  The current state of the game.

    Returns:    The action to be taken from the current state

    """
    bot_identity = board.current_player(current_state) # 1 or 2
    root_node = MCTSNode(parent=None, parent_action=None, action_list=board.legal_actions(current_state))

    for _ in range(num_nodes):
        state = current_state
        node = root_node

        # 第一步：选择 - 遍历树找到最佳可扩展节点
        node, state = traverse_nodes(node, board, state, bot_identity)

        # 第二步：扩展 - 如果可能，添加新的子节点
        node, state = expand_leaf(node, board, state)

        # 第三步：模拟 - 使用启发式策略模拟游戏到结束
        terminal_state = rollout(board, state)

        # 第四步：反向传播 - 更新路径上所有节点的统计信息
        won = is_win(board, terminal_state, bot_identity)
        backpropagate(node, won)

    # Return an action, typically the most frequently used action (from the root) or the action with the best
    # estimated win rate.
    best_action = get_best_action(root_node)

    print(f"Modified MCTS Action chosen: {best_action}")
    return best_action
