#!/usr/bin/env python3
"""
实验1：测试不同树大小对vanilla MCTS性能的影响
Player 1固定为100节点，Player 2测试不同的树大小
"""

import sys
import os
from timeit import default_timer as time
import matplotlib.pyplot as plt
import numpy as np

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import p2_t3
import mcts_vanilla
import random_bot

def run_games(player1_func, player2_func, num_games=100):
    """运行指定数量的游戏并返回结果统计"""
    board = p2_t3.Board()
    state0 = board.starting_state()
    
    wins = {'draw': 0, 1: 0, 2: 0}
    
    for i in range(num_games):
        if i % 10 == 0:
            print(f"  游戏 {i+1}/{num_games}")
        
        state = state0
        current_player = player1_func
        
        while not board.is_ended(state):
            last_action = current_player(board, state)
            state = board.next_state(state, last_action)
            current_player = player1_func if current_player == player2_func else player2_func
        
        final_score = board.points_values(state)
        winner = 'draw'
        if final_score[1] == 1:
            winner = 1
        elif final_score[2] == 1:
            winner = 2
        
        wins[winner] = wins.get(winner, 0) + 1
    
    return wins

def modify_tree_size(size):
    """修改mcts_vanilla的树大小"""
    mcts_vanilla.num_nodes = size

def experiment1():
    """实验1主函数"""
    print("开始实验1：测试不同树大小对vanilla MCTS性能的影响")
    print("=" * 60)
    
    # 测试的树大小
    tree_sizes = [50, 100, 200, 500, 1000]
    num_games = 100
    
    # 固定Player 1为100节点
    player1_tree_size = 100
    
    results = []
    
    for tree_size in tree_sizes:
        print(f"\n测试树大小: Player1={player1_tree_size}, Player2={tree_size}")
        print("-" * 40)
        
        # 设置Player 1的树大小
        modify_tree_size(player1_tree_size)
        player1_func = mcts_vanilla.think
        
        # 设置Player 2的树大小
        modify_tree_size(tree_size)
        player2_func = mcts_vanilla.think
        
        start_time = time()
        wins = run_games(player1_func, player2_func, num_games)
        end_time = time()
        
        # 计算Player 2的胜率
        player2_wins = wins[2]
        player2_win_rate = player2_wins / num_games
        
        results.append({
            'tree_size': tree_size,
            'player2_wins': player2_wins,
            'player2_win_rate': player2_win_rate,
            'total_games': num_games,
            'time_elapsed': end_time - start_time
        })
        
        print(f"结果: Player2胜利 {player2_wins}/{num_games} 场 ({player2_win_rate:.2%})")
        print(f"用时: {end_time - start_time:.2f} 秒")
    
    # 生成图表
    generate_plot(results)
    
    # 保存结果
    save_results(results)
    
    return results

def generate_plot(results):
    """生成实验结果图表"""
    tree_sizes = [r['tree_size'] for r in results]
    win_rates = [r['player2_win_rate'] for r in results]
    
    plt.figure(figsize=(10, 6))
    plt.plot(tree_sizes, win_rates, 'bo-', linewidth=2, markersize=8)
    plt.xlabel('Player 2 树大小 (节点数)', fontsize=12)
    plt.ylabel('Player 2 胜率', fontsize=12)
    plt.title('实验1: 不同树大小对MCTS性能的影响\n(Player 1固定为100节点)', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)
    
    # 添加数据标签
    for i, (x, y) in enumerate(zip(tree_sizes, win_rates)):
        plt.annotate(f'{y:.2%}', (x, y), textcoords="offset points", 
                    xytext=(0,10), ha='center')
    
    plt.tight_layout()
    plt.savefig('2808092173544445255/experiment1_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n图表已保存为: experiment1_results.png")

def save_results(results):
    """保存实验结果到文件"""
    with open('2808092173544445255/experiment1_data.txt', 'w', encoding='utf-8') as f:
        f.write("实验1结果：不同树大小对vanilla MCTS性能的影响\n")
        f.write("=" * 50 + "\n")
        f.write("Player 1固定为100节点\n\n")
        
        f.write("树大小\t胜场数\t胜率\t总游戏数\t用时(秒)\n")
        f.write("-" * 50 + "\n")
        
        for r in results:
            f.write(f"{r['tree_size']}\t{r['player2_wins']}\t{r['player2_win_rate']:.2%}\t"
                   f"{r['total_games']}\t{r['time_elapsed']:.2f}\n")
    
    print(f"详细结果已保存为: experiment1_data.txt")

if __name__ == "__main__":
    results = experiment1()
    
    print("\n" + "=" * 60)
    print("实验1完成！")
    print("主要发现:")
    
    # 分析结果
    best_result = max(results, key=lambda x: x['player2_win_rate'])
    print(f"- 最佳树大小: {best_result['tree_size']} 节点 (胜率: {best_result['player2_win_rate']:.2%})")
    
    # 计算性能提升
    baseline = next(r for r in results if r['tree_size'] == 100)
    improvements = []
    for r in results:
        if r['tree_size'] != 100:
            improvement = r['player2_win_rate'] - baseline['player2_win_rate']
            improvements.append((r['tree_size'], improvement))
    
    print("- 相对于100节点的性能变化:")
    for size, improvement in improvements:
        print(f"  {size}节点: {improvement:+.2%}")
