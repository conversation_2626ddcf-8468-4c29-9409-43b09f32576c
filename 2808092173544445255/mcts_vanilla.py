
from mcts_node import MCTSNode
from p2_t3 import Board
from random import choice
from math import sqrt, log

num_nodes = 100
explore_faction = 2.

def traverse_nodes(node: MCTSNode, board: Board, state, bot_identity: int):
    """ Traverses the tree until the end criterion are met.
    e.g. find the best expandable node (node with untried action) if it exist,
    or else a terminal node

    Args:
        node:       A tree node from which the search is traversing.
        board:      The game setup.
        state:      The state of the game.
        identity:   The bot's identity, either 1 or 2

    Returns:
        node: A node from which the next stage of the search can proceed.
        state: The state associated with that node

    """
    # 继续遍历直到找到可扩展的节点或终端节点
    while not board.is_ended(state) and len(node.untried_actions) == 0 and len(node.child_nodes) > 0:
        # 当前玩家是否为对手
        current_player = board.current_player(state)
        is_opponent = (current_player != bot_identity)

        # 使用UCB选择最佳子节点
        best_action = None
        best_ucb_value = float('-inf')

        for action, child_node in node.child_nodes.items():
            ucb_value = ucb(child_node, is_opponent)
            if ucb_value > best_ucb_value:
                best_ucb_value = ucb_value
                best_action = action

        # 移动到选中的子节点
        node = node.child_nodes[best_action]
        state = board.next_state(state, best_action)

    return node, state

def expand_leaf(node: MCTSNode, board: Board, state):
    """ Adds a new leaf to the tree by creating a new child node for the given node (if it is non-terminal).

    Args:
        node:   The node for which a child will be added.
        board:  The game setup.
        state:  The state of the game.

    Returns:
        node: The added child node
        state: The state associated with that node

    """
    # 如果游戏已结束或没有未尝试的动作，返回当前节点
    if board.is_ended(state) or len(node.untried_actions) == 0:
        return node, state

    # 随机选择一个未尝试的动作
    action = choice(node.untried_actions)

    # 从未尝试列表中移除该动作
    node.untried_actions.remove(action)

    # 计算新状态
    new_state = board.next_state(state, action)

    # 创建新的子节点
    child_node = MCTSNode(parent=node, parent_action=action,
                         action_list=board.legal_actions(new_state))

    # 将子节点添加到父节点的子节点字典中
    node.child_nodes[action] = child_node

    return child_node, new_state


def rollout(board: Board, state):
    """ Given the state of the game, the rollout plays out the remainder randomly.

    Args:
        board:  The game setup.
        state:  The state of the game.

    Returns:
        state: The terminal game state

    """
    # 随机模拟游戏直到结束
    current_state = state

    while not board.is_ended(current_state):
        # 获取所有合法动作
        legal_actions = board.legal_actions(current_state)

        # 如果没有合法动作，游戏结束
        if not legal_actions:
            break

        # 随机选择一个动作
        action = choice(legal_actions)

        # 执行动作，更新状态
        current_state = board.next_state(current_state, action)

    return current_state


def backpropagate(node, won: bool):
    """ Navigates the tree from a leaf node to the root, updating the win and visit count of each node along the path.

    Args:
        node:   A leaf node.
        won:    An indicator of whether the bot won or lost the game.

    """
    # 从叶节点向根节点回溯，更新每个节点的统计信息
    current_node = node

    while current_node is not None:
        # 增加访问次数
        current_node.visits += 1

        # 如果获胜，增加胜利次数
        if won:
            current_node.wins += 1

        # 移动到父节点
        current_node = current_node.parent

def ucb(node: MCTSNode, is_opponent: bool):
    """ Calcualtes the UCB value for the given node from the perspective of the bot

    Args:
        node:   A node.
        is_opponent: A boolean indicating whether or not the last action was performed by the MCTS bot
    Returns:
        The value of the UCB function for the given node
    """
    # 如果节点从未被访问过，返回无穷大
    if node.visits == 0:
        return float('inf')

    # 计算胜率
    win_rate = node.wins / node.visits

    # 对于对手的回合，需要反转胜率
    if is_opponent:
        win_rate = 1.0 - win_rate

    # 计算探索项
    if node.parent is None or node.parent.visits == 0:
        exploration = 0
    else:
        exploration = explore_faction * sqrt(log(node.parent.visits) / node.visits)

    # UCB值 = 利用项 + 探索项
    return win_rate + exploration

def get_best_action(root_node: MCTSNode):
    """ Selects the best action from the root node in the MCTS tree

    Args:
        root_node:   The root node
    Returns:
        action: The best action from the root node

    """
    # 如果没有子节点，返回None
    if not root_node.child_nodes:
        return None

    # 选择访问次数最多的动作（最稳健的策略）
    best_action = None
    most_visits = -1

    for action, child_node in root_node.child_nodes.items():
        if child_node.visits > most_visits:
            most_visits = child_node.visits
            best_action = action

    return best_action

def is_win(board: Board, state, identity_of_bot: int):
    # checks if state is a win state for identity_of_bot
    outcome = board.points_values(state)
    assert outcome is not None, "is_win was called on a non-terminal state"
    return outcome[identity_of_bot] == 1

def think(board: Board, current_state):
    """ Performs MCTS by sampling games and calling the appropriate functions to construct the game tree.

    Args:
        board:  The game setup.
        current_state:  The current state of the game.

    Returns:    The action to be taken from the current state

    """
    bot_identity = board.current_player(current_state) # 1 or 2
    root_node = MCTSNode(parent=None, parent_action=None, action_list=board.legal_actions(current_state))

    for _ in range(num_nodes):
        state = current_state
        node = root_node

        # 第一步：选择 - 遍历树找到最佳可扩展节点
        node, state = traverse_nodes(node, board, state, bot_identity)

        # 第二步：扩展 - 如果可能，添加新的子节点
        node, state = expand_leaf(node, board, state)

        # 第三步：模拟 - 随机模拟游戏到结束
        terminal_state = rollout(board, state)

        # 第四步：反向传播 - 更新路径上所有节点的统计信息
        won = is_win(board, terminal_state, bot_identity)
        backpropagate(node, won)

    # Return an action, typically the most frequently used action (from the root) or the action with the best
    # estimated win rate.
    best_action = get_best_action(root_node)
    
    print(f"Action chosen: {best_action}")
    return best_action
