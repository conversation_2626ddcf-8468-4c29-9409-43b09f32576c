#!/usr/bin/env python3
"""
快速测试脚本：验证MCTS实现的基本功能
运行少量游戏来确保代码正常工作
"""

import sys
import os
from timeit import default_timer as time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import p2_t3
import mcts_vanilla
import mcts_modified
import random_bot

def quick_test_vanilla():
    """快速测试vanilla MCTS"""
    print("测试 Vanilla MCTS vs Random Bot (5场游戏)")
    
    board = p2_t3.Board()
    state0 = board.starting_state()
    
    # 设置小树大小以加快测试
    mcts_vanilla.num_nodes = 20
    
    wins = {'vanilla': 0, 'random': 0, 'draw': 0}
    
    for i in range(5):
        print(f"  游戏 {i+1}/5", end=" ")
        
        state = state0
        current_player = mcts_vanilla.think
        
        move_count = 0
        while not board.is_ended(state) and move_count < 100:  # 限制最大步数
            if current_player == mcts_vanilla.think:
                action = mcts_vanilla.think(board, state)
            else:
                action = random_bot.think(board, state)
            
            state = board.next_state(state, action)
            current_player = mcts_vanilla.think if current_player == random_bot.think else random_bot.think
            move_count += 1
        
        if board.is_ended(state):
            final_score = board.points_values(state)
            if final_score[1] == 1:
                wins['vanilla'] += 1
                print("- Vanilla胜利")
            elif final_score[2] == 1:
                wins['random'] += 1
                print("- Random胜利")
            else:
                wins['draw'] += 1
                print("- 平局")
        else:
            wins['draw'] += 1
            print("- 超时平局")
    
    print(f"结果: Vanilla {wins['vanilla']}, Random {wins['random']}, 平局 {wins['draw']}")
    return wins['vanilla'] >= 3  # 至少赢3场算成功

def quick_test_modified():
    """快速测试modified MCTS"""
    print("\n测试 Modified MCTS vs Random Bot (5场游戏)")
    
    board = p2_t3.Board()
    state0 = board.starting_state()
    
    # 设置小树大小以加快测试
    mcts_modified.num_nodes = 20
    
    wins = {'modified': 0, 'random': 0, 'draw': 0}
    
    for i in range(5):
        print(f"  游戏 {i+1}/5", end=" ")
        
        state = state0
        current_player = mcts_modified.think
        
        move_count = 0
        while not board.is_ended(state) and move_count < 100:  # 限制最大步数
            if current_player == mcts_modified.think:
                action = mcts_modified.think(board, state)
            else:
                action = random_bot.think(board, state)
            
            state = board.next_state(state, action)
            current_player = mcts_modified.think if current_player == random_bot.think else random_bot.think
            move_count += 1
        
        if board.is_ended(state):
            final_score = board.points_values(state)
            if final_score[1] == 1:
                wins['modified'] += 1
                print("- Modified胜利")
            elif final_score[2] == 1:
                wins['random'] += 1
                print("- Random胜利")
            else:
                wins['draw'] += 1
                print("- 平局")
        else:
            wins['draw'] += 1
            print("- 超时平局")
    
    print(f"结果: Modified {wins['modified']}, Random {wins['random']}, 平局 {wins['draw']}")
    return wins['modified'] >= 3  # 至少赢3场算成功

def quick_test_comparison():
    """快速测试vanilla vs modified"""
    print("\n测试 Vanilla vs Modified MCTS (3场游戏)")
    
    board = p2_t3.Board()
    state0 = board.starting_state()
    
    # 设置相同的小树大小
    mcts_vanilla.num_nodes = 20
    mcts_modified.num_nodes = 20
    
    wins = {'vanilla': 0, 'modified': 0, 'draw': 0}
    
    for i in range(3):
        print(f"  游戏 {i+1}/3", end=" ")
        
        state = state0
        # 交替先手
        if i % 2 == 0:
            current_player = mcts_vanilla.think
            players = [mcts_vanilla.think, mcts_modified.think]
        else:
            current_player = mcts_modified.think
            players = [mcts_modified.think, mcts_vanilla.think]
        
        move_count = 0
        while not board.is_ended(state) and move_count < 100:  # 限制最大步数
            action = current_player(board, state)
            state = board.next_state(state, action)
            current_player = players[1] if current_player == players[0] else players[0]
            move_count += 1
        
        if board.is_ended(state):
            final_score = board.points_values(state)
            if final_score[1] == 1:
                winner = players[0]
            elif final_score[2] == 1:
                winner = players[1]
            else:
                winner = None
            
            if winner == mcts_vanilla.think:
                wins['vanilla'] += 1
                print("- Vanilla胜利")
            elif winner == mcts_modified.think:
                wins['modified'] += 1
                print("- Modified胜利")
            else:
                wins['draw'] += 1
                print("- 平局")
        else:
            wins['draw'] += 1
            print("- 超时平局")
    
    print(f"结果: Vanilla {wins['vanilla']}, Modified {wins['modified']}, 平局 {wins['draw']}")
    return wins

def main():
    """主测试函数"""
    print("MCTS实现快速测试")
    print("=" * 50)
    
    start_time = time()
    
    # 测试vanilla MCTS
    vanilla_success = quick_test_vanilla()
    
    # 测试modified MCTS
    modified_success = quick_test_modified()
    
    # 测试两者对比
    comparison_results = quick_test_comparison()
    
    end_time = time()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"- Vanilla MCTS: {'✓ 通过' if vanilla_success else '✗ 失败'}")
    print(f"- Modified MCTS: {'✓ 通过' if modified_success else '✗ 失败'}")
    print(f"- 对比测试: Vanilla {comparison_results['vanilla']}, Modified {comparison_results['modified']}, 平局 {comparison_results['draw']}")
    print(f"- 总用时: {end_time - start_time:.2f} 秒")
    
    if vanilla_success and modified_success:
        print("\n✓ 所有基本测试通过！可以运行完整实验。")
        print("\n运行完整实验:")
        print("  python experiment1.py  # 树大小影响实验")
        print("  python experiment2.py  # 算法对比实验")
    else:
        print("\n✗ 部分测试失败，请检查实现。")
    
    return vanilla_success and modified_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
