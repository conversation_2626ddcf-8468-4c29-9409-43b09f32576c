# Ultimate Tic-Tac-Toe MCTS Implementation

## 团队成员
- 张三 (学号: 2021001001)
- 李四 (学号: 2021001002)

## 项目概述

本项目实现了用于Ultimate Tic-Tac-Toe游戏的Monte Carlo Tree Search (MCTS)算法，包括标准版本(vanilla)和改进版本(modified)。

## 文件结构

```
├── p2_t3.py              # 游戏逻辑和棋盘实现
├── mcts_node.py          # MCTS节点类定义
├── mcts_vanilla.py       # 标准MCTS实现
├── mcts_modified.py      # 改进MCTS实现
├── random_bot.py         # 随机策略机器人
├── rollout_bot.py        # 基于rollout的机器人
├── p2_play.py           # 交互式游戏界面
├── p2_sim.py            # 游戏模拟器
├── experiment1.py       # 实验1：树大小影响测试
├── experiment2.py       # 实验2：vanilla vs modified比较
└── README.md            # 本文档
```

## MCTS算法实现

### Vanilla版本 (mcts_vanilla.py)

实现了标准的MCTS算法，包含四个核心阶段：

1. **选择 (Selection)**: 使用UCB1公式遍历树，选择最有前景的节点
2. **扩展 (Expansion)**: 为选中的节点添加新的子节点
3. **模拟 (Simulation)**: 从新节点开始随机模拟游戏到结束
4. **反向传播 (Backpropagation)**: 将模拟结果向上传播更新所有路径节点

#### 核心函数：
- `traverse_nodes()`: 树遍历和节点选择
- `expand_leaf()`: 节点扩展
- `rollout()`: 随机模拟
- `backpropagate()`: 结果反向传播
- `ucb()`: UCB1值计算
- `get_best_action()`: 最佳动作选择

### Modified版本 (mcts_modified.py)

在vanilla版本基础上添加了多项启发式改进：

#### 主要改进：

1. **智能Rollout策略**
   - 80%概率使用启发式策略，20%概率随机选择
   - 优先考虑获胜动作、阻止对手获胜、中心位置

2. **改进的UCB公式**
   - 在标准UCB基础上添加位置启发式奖励
   - 考虑动作的战略价值

3. **启发式动作选择**
   - 使用轮盘赌选择机制，偏向高价值动作
   - 综合考虑位置价值、获胜机会、防守需求

4. **改进的最终动作选择**
   - 结合胜率和访问次数的加权评分
   - 更稳健的决策机制

#### 启发式权重：
- `CENTER_WEIGHT = 1.5`: 中心位置权重
- `BLOCK_WEIGHT = 2.0`: 阻止对手获胜权重  
- `WIN_WEIGHT = 3.0`: 获胜动作权重

#### 核心改进函数：
- `ucb_improved()`: 改进的UCB计算
- `select_action_heuristic()`: 启发式动作选择
- `evaluate_action_heuristic()`: 动作价值评估

## 运行说明

### 交互式游戏
```bash
python p2_play.py human mcts_vanilla
python p2_play.py mcts_vanilla mcts_modified
```

### 游戏模拟
```bash
python p2_sim.py mcts_vanilla mcts_modified
```

### 实验运行
```bash
# 实验1：测试不同树大小的影响
python experiment1.py

# 实验2：比较vanilla和modified版本
python experiment2.py
```

## 实验设计

### 实验1：树大小影响分析
- **目标**: 测试不同树大小对MCTS性能的影响
- **设置**: Player 1固定100节点，Player 2测试50, 100, 200, 500, 1000节点
- **游戏数**: 每个配置100场游戏
- **输出**: 胜率图表和详细数据

### 实验2：算法版本比较
- **目标**: 比较vanilla和modified版本的性能
- **设置**: 两个版本使用相同树大小(500, 1000, 1500节点)
- **游戏数**: 每个配置200场游戏(双向对战各100场)
- **输出**: 性能对比图表和统计分析

## 技术特点

### 对抗性规划处理
- 正确处理双人零和游戏的UCB计算
- 对手回合时反转胜率评估
- 确保算法从当前玩家视角进行优化

### 性能优化
- 高效的状态表示和动作生成
- 优化的树遍历算法
- 智能的内存管理

### 启发式策略
- 基于游戏规则的位置评估
- 战术感知的动作优先级
- 平衡探索与利用的策略

## 预期结果

1. **树大小影响**: 更大的树通常带来更好的性能，但收益递减
2. **算法改进**: Modified版本应该在大多数情况下优于vanilla版本
3. **计算效率**: 启发式改进可能略微增加单次模拟时间，但提高决策质量

## 依赖项

- Python 3.7+
- matplotlib (用于图表生成)
- numpy (用于数值计算)

## 安装依赖
```bash
pip install matplotlib numpy
```

## 注意事项

1. 大树大小(1000+节点)的实验可能需要较长时间
2. 可以通过减少游戏数量来加快测试速度
3. 实验结果会保存为PNG图片和TXT数据文件
4. 建议在性能较好的机器上运行完整实验
