#!/usr/bin/env python3
"""
基于最新实验结果重新生成图表
解决中文字体问题
"""

import json
import matplotlib
import matplotlib.pyplot as plt

# 配置字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans', 'Arial']
matplotlib.rcParams['axes.unicode_minus'] = False

def regenerate_experiment1_chart():
    """基于最新结果重新生成实验1图表"""
    # 最新实验1结果
    results = {
        "25": {
            "wins": {"draw": 1, "1": 43, "2": 6},
            "player2_win_rate": 0.12,
            "time": 53.4
        },
        "50": {
            "wins": {"draw": 0, "1": 36, "2": 14},
            "player2_win_rate": 0.28,
            "time": 64.2
        },
        "100": {
            "wins": {"draw": 0, "1": 28, "2": 22},
            "player2_win_rate": 0.44,
            "time": 82.6
        },
        "200": {
            "wins": {"draw": 0, "1": 17, "2": 33},
            "player2_win_rate": 0.66,
            "time": 120.7
        },
        "400": {
            "wins": {"draw": 0, "1": 8, "2": 42},
            "player2_win_rate": 0.84,
            "time": 191.9
        }
    }
    
    tree_sizes = [int(size) for size in results.keys()]
    win_rates = [results[str(size)]['player2_win_rate'] for size in tree_sizes]
    times = [results[str(size)]['time'] for size in tree_sizes]
    
    # 创建双Y轴图表
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 胜率曲线
    color = 'tab:blue'
    ax1.set_xlabel('Tree Size (Nodes)', fontsize=14)
    ax1.set_ylabel('Player 2 Win Rate', color=color, fontsize=14)
    line1 = ax1.plot(tree_sizes, win_rates, 'o-', color=color, linewidth=3, markersize=8, label='Win Rate')
    ax1.tick_params(axis='y', labelcolor=color)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 1)
    
    # 添加胜率数据标签
    for x, y in zip(tree_sizes, win_rates):
        ax1.annotate(f'{y:.0%}', (x, y), textcoords="offset points", 
                    xytext=(0,15), ha='center', fontweight='bold', fontsize=11)
    
    # 时间曲线（第二个Y轴）
    ax2 = ax1.twinx()
    color = 'tab:red'
    ax2.set_ylabel('Computation Time (seconds)', color=color, fontsize=14)
    line2 = ax2.plot(tree_sizes, times, 's-', color=color, linewidth=2, markersize=6, alpha=0.7, label='Time')
    ax2.tick_params(axis='y', labelcolor=color)
    
    # 添加时间数据标签
    for x, y in zip(tree_sizes, times):
        ax2.annotate(f'{y:.0f}s', (x, y), textcoords="offset points", 
                    xytext=(0,-20), ha='center', color=color, fontsize=10)
    
    # 图表标题和图例
    plt.title('Experiment 1: MCTS Performance vs Tree Size\n(Player 1 Fixed at 100 Nodes, 50 games each)', 
              fontsize=16, fontweight='bold', pad=20)
    
    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='center left', bbox_to_anchor=(0.02, 0.5))
    
    plt.tight_layout()
    plt.savefig('experiment1_latest_results.png', dpi=300, bbox_inches='tight')
    print("✅ 最新实验1图表已生成: experiment1_latest_results.png")
    plt.close()

def create_performance_analysis():
    """创建性能分析图表"""
    tree_sizes = [25, 50, 100, 200, 400]
    win_rates = [0.12, 0.28, 0.44, 0.66, 0.84]
    times = [53.4, 64.2, 82.6, 120.7, 191.9]
    
    # 计算效率指标
    efficiency = [(w/t)*1000 for w, t in zip(win_rates, times)]  # 胜率/时间 * 1000
    time_per_node = [t/size for t, size in zip(times, tree_sizes)]  # 每节点耗时
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 胜率 vs 树大小
    ax1.plot(tree_sizes, win_rates, 'bo-', linewidth=2, markersize=8)
    ax1.set_xlabel('Tree Size (Nodes)')
    ax1.set_ylabel('Win Rate')
    ax1.set_title('Win Rate vs Tree Size')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 1)
    for x, y in zip(tree_sizes, win_rates):
        ax1.annotate(f'{y:.0%}', (x, y), textcoords="offset points", 
                    xytext=(0,10), ha='center')
    
    # 2. 时间 vs 树大小
    ax2.plot(tree_sizes, times, 'ro-', linewidth=2, markersize=8)
    ax2.set_xlabel('Tree Size (Nodes)')
    ax2.set_ylabel('Computation Time (seconds)')
    ax2.set_title('Computation Time vs Tree Size')
    ax2.grid(True, alpha=0.3)
    for x, y in zip(tree_sizes, times):
        ax2.annotate(f'{y:.0f}s', (x, y), textcoords="offset points", 
                    xytext=(0,10), ha='center')
    
    # 3. 效率指标
    ax3.bar(tree_sizes, efficiency, color='green', alpha=0.7)
    ax3.set_xlabel('Tree Size (Nodes)')
    ax3.set_ylabel('Efficiency (Win Rate / Time × 1000)')
    ax3.set_title('Performance Efficiency')
    ax3.grid(True, alpha=0.3)
    for x, y in zip(tree_sizes, efficiency):
        ax3.annotate(f'{y:.1f}', (x, y), textcoords="offset points", 
                    xytext=(0,5), ha='center')
    
    # 4. 每节点耗时
    ax4.plot(tree_sizes, time_per_node, 'mo-', linewidth=2, markersize=8)
    ax4.set_xlabel('Tree Size (Nodes)')
    ax4.set_ylabel('Time per Node (seconds)')
    ax4.set_title('Time Efficiency per Node')
    ax4.grid(True, alpha=0.3)
    for x, y in zip(tree_sizes, time_per_node):
        ax4.annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                    xytext=(0,10), ha='center')
    
    plt.suptitle('MCTS Performance Analysis - Experiment 1', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('experiment1_analysis.png', dpi=300, bbox_inches='tight')
    print("✅ 性能分析图表已生成: experiment1_analysis.png")
    plt.close()

def main():
    print("🎨 重新生成实验1图表")
    print("=" * 40)
    
    print("📊 最新实验1结果:")
    print("  25节点: 12%胜率, 53.4s")
    print("  50节点: 28%胜率, 64.2s")
    print("  100节点: 44%胜率, 82.6s")
    print("  200节点: 66%胜率, 120.7s")
    print("  400节点: 84%胜率, 191.9s")
    
    print("\n生成图表...")
    regenerate_experiment1_chart()
    create_performance_analysis()
    
    print("\n🎉 图表生成完成!")
    print("生成的文件:")
    print("  - experiment1_latest_results.png (主要结果图)")
    print("  - experiment1_analysis.png (详细分析图)")

if __name__ == "__main__":
    main()