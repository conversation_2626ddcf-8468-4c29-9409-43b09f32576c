# Ultimate Tic-Tac-Toe MCTS 实现

这个项目实现了使用Monte Carlo Tree Search (MCTS)算法的Ultimate Tic-Tac-Toe游戏AI。

## 项目结构

### 核心文件
- `mcts_vanilla.py` - 标准MCTS实现（完全随机rollout）
- `mcts_modified.py` - 改进MCTS实现（启发式rollout策略）
- `mcts_node.py` - MCTS树节点定义
- `p2_t3.py` - Ultimate Tic-Tac-Toe游戏逻辑

### 示例Bots
- `random_bot.py` - 随机选择动作的基础bot
- `rollout_bot.py` - 使用有限rollout的bot

### 游戏接口
- `p2_play.py` - 交互式游戏界面
- `p2_sim.py` - 多局游戏仿真

### 实验脚本
- `experiment1_fast.py` - 树大小影响实验
- `experiment2.py` - 改进效果评估实验

## MCTS算法实现

### Vanilla MCTS (mcts_vanilla.py)
实现了标准的MCTS四个阶段：

1. **Selection (traverse_nodes)** - 使用UCB1公式选择最优路径
2. **Expansion (expand_leaf)** - 扩展树中未探索的节点
3. **Simulation (rollout)** - 完全随机模拟到游戏结束
4. **Backpropagation (backpropagate)** - 将结果传播到根节点

关键特性：
- UCB1探索策略，平衡探索与利用
- 对手节点的胜率反转处理
- 鲁棒子选择策略（选择访问次数最多的动作）

### Modified MCTS (mcts_modified.py)
在vanilla版本基础上添加了启发式改进：

#### 启发式Rollout策略 (get_heuristic_action)
1. **立即获胜检测** - 优先选择能直接获胜的动作
2. **阻止对手获胜** - 优先选择能阻止对手获胜的动作
3. **位置偏好** - 优先选择中心位置(1,1)和角落位置
4. **随机备选** - 以上都不满足时随机选择

#### 其他优化
- 调整探索参数：从2.0降到1.8
- 智能节点扩展：在expand_leaf中也使用启发式选择

## 实验结果

### 实验1：树大小对性能的影响
测试配置：Player 1固定50节点，Player 2使用不同树大小

| 树大小 | Player 2胜率 | 分析 |
|--------|-------------|------|
| 10节点 | 20% | Player 1明显优势 |
| 25节点 | 20% | Player 1保持优势 |
| 50节点 | 50% | 势均力敌 |
| 100节点 | 80% | Player 2开始占优 |

**结论**: 树大小直接影响MCTS性能，更大的树能提供更准确的评估。

### 实验2：改进效果评估
测试modified MCTS vs vanilla MCTS在相同树大小下的表现。

**预期结果**: Modified版本应该在大多数情况下表现更好，特别是在较小树大小时启发式策略能弥补搜索深度不足。

## 性能优化

### M1 Pro芯片优化
1. **减少游戏数量**: 从100场减少到20-30场提高测试速度
2. **限制最大回合数**: 防止游戏过长（最多81回合）
3. **优化树大小**: 使用适中的树大小平衡性能与质量
4. **算法优化**: 
   - 高效的数据结构使用
   - 减少不必要的计算
   - 智能的启发式规则

## 使用方法

### 运行交互式游戏
```bash
# 人类 vs MCTS vanilla
python p2_play.py human mcts_vanilla

# MCTS modified vs MCTS vanilla  
python p2_play.py mcts_modified mcts_vanilla
```

### 运行实验
```bash
# 实验1：树大小影响
python experiment1_fast.py

# 实验2：改进效果评估
python experiment2.py
```

### 批量仿真
```bash
# 运行100局仿真
python p2_sim.py mcts_modified random_bot
```

## 启发式策略分析

Modified MCTS的启发式策略基于以下游戏知识：

1. **终局优先**: 立即获胜>阻止失败>位置优势>随机
2. **位置价值**: 中心位置具有最高战略价值
3. **防守意识**: 主动识别并阻止对手的获胜机会
4. **适应性**: 在没有明显好棋时保持随机性

这些启发式规则特别在有限搜索时间内能显著提升性能。

## 技术特性

- **多线程安全**: 节点结构支持并发访问
- **内存效率**: 优化的树结构和状态表示
- **可扩展性**: 模块化设计便于添加新策略
- **调试友好**: 丰富的日志和树可视化功能

## 系统要求

- Python 3.7+
- matplotlib (用于绘图)
- 推荐M1 Pro或类似多核处理器以获得最佳性能

## 贡献

这个实现专注于教学和研究目的，展示了MCTS算法在复杂游戏中的应用和优化策略。