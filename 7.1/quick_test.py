#!/usr/bin/env python3

import sys
import p2_t3
import mcts_vanilla
import mcts_modified
import random_bot

# 快速测试两个bot是否工作
def quick_test():
    board = p2_t3.Board()
    state = board.starting_state()
    
    print("Testing mcts_vanilla...")
    try:
        action = mcts_vanilla.think(board, state)
        print(f"✓ mcts_vanilla returned action: {action}")
    except Exception as e:
        print(f"✗ mcts_vanilla failed: {e}")
        return False
    
    print("\nTesting mcts_modified...")
    try:
        action = mcts_modified.think(board, state)
        print(f"✓ mcts_modified returned action: {action}")
    except Exception as e:
        print(f"✗ mcts_modified failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = quick_test()
    if success:
        print("\n✓ All bots working correctly!")
    else:
        print("\n✗ Some bots have issues!")
        sys.exit(1)