
from mcts_node import MCTSNode
from p2_t3 import Board, positions
from random import choice
from math import sqrt, log

num_nodes = 100
explore_faction = 1.8  # 调整探索参数

def traverse_nodes(node: MCTSNode, board: Board, state, bot_identity: int):
    """ Traverses the tree until the end criterion are met.
    e.g. find the best expandable node (node with untried action) if it exist,
    or else a terminal node

    Args:
        node:       A tree node from which the search is traversing.
        board:      The game setup.
        state:      The state of the game.
        identity:   The bot's identity, either 1 or 2

    Returns:
        node: A node from which the next stage of the search can proceed.
        state: The state associated with that node

    """
    # 遍历树直到找到可扩展的节点或终端节点
    while not board.is_ended(state):
        # 如果有未尝试的动作，返回此节点用于扩展
        if node.untried_actions:
            return node, state
        
        # 如果所有动作都已尝试，选择最佳子节点
        if node.child_nodes:
            # 确定当前玩家是否是对手
            current_player = board.current_player(state)
            is_opponent = (current_player != bot_identity)
            
            # 使用UCB选择最佳子节点
            best_action = max(node.child_nodes.keys(), 
                            key=lambda a: ucb(node.child_nodes[a], is_opponent))
            node = node.child_nodes[best_action]
            state = board.next_state(state, best_action)
        else:
            # 没有子节点且没有未尝试动作，返回此节点
            return node, state
    
    return node, state

def expand_leaf(node: MCTSNode, board: Board, state):
    """ Adds a new leaf to the tree by creating a new child node for the given node (if it is non-terminal).

    Args:
        node:   The node for which a child will be added.
        board:  The game setup.
        state:  The state of the game.

    Returns:
        node: The added child node
        state: The state associated with that node

    """
    # 如果游戏已结束或没有未尝试的动作，返回当前节点
    if board.is_ended(state) or not node.untried_actions:
        return node, state
    
    # 智能选择未尝试的动作（优先选择更好的动作）
    best_action = get_heuristic_action(node.untried_actions, board, state)
    
    # 从未尝试列表中移除该动作
    node.untried_actions.remove(best_action)
    
    # 计算新状态
    new_state = board.next_state(state, best_action)
    
    # 创建新的子节点
    child_node = MCTSNode(parent=node, 
                         parent_action=best_action, 
                         action_list=board.legal_actions(new_state))
    
    # 将子节点添加到父节点的child_nodes字典中
    node.child_nodes[best_action] = child_node
    
    return child_node, new_state


def heuristic_rollout(board: Board, state):
    """ 启发式rollout，使用游戏知识而不是完全随机 """
    while not board.is_ended(state):
        legal_actions = board.legal_actions(state)
        
        if not legal_actions:
            break
        
        # 使用启发式策略选择动作
        action = get_heuristic_action(legal_actions, board, state)
        state = board.next_state(state, action)
    
    return state

def rollout(board: Board, state):
    """ Given the state of the game, the rollout plays out the remainder randomly.

    Args:
        board:  The game setup.
        state:  The state of the game.
    
    Returns:
        state: The terminal game state

    """
    # 使用启发式rollout而不是完全随机
    return heuristic_rollout(board, state)

def get_heuristic_action(actions, board, state):
    """ 启发式动作选择：优先考虑获胜、阻止对手获胜、控制中心等策略 """
    current_player = board.current_player(state)
    opponent = 3 - current_player
    
    # 1. 检查是否有立即获胜的动作
    for action in actions:
        test_state = board.next_state(state, action)
        if board.is_ended(test_state):
            outcome = board.points_values(test_state)
            if outcome and outcome[current_player] == 1:
                return action
    
    # 2. 检查是否需要阻止对手获胜
    for action in actions:
        # 模拟对手在此位置放置后的状态
        temp_state = list(state)
        R, C, r, c = action
        board_index = 2 * (3 * R + C)
        opponent_index = opponent - 1
        temp_state[board_index + opponent_index] |= positions[(r, c)]
        temp_state = tuple(temp_state)
        
        # 检查对手是否会获胜
        if board.is_ended(temp_state):
            outcome = board.points_values(temp_state)
            if outcome and outcome[opponent] == 1:
                return action
    
    # 3. 优先选择中心位置和角落位置
    center_actions = [a for a in actions if a[2] == 1 and a[3] == 1]  # 中心
    if center_actions:
        return choice(center_actions)
    
    corner_actions = [a for a in actions if (a[2], a[3]) in [(0,0), (0,2), (2,0), (2,2)]]
    if corner_actions:
        return choice(corner_actions)
    
    # 4. 如果以上都不满足，随机选择
    return choice(actions)


def backpropagate(node, won: bool):
    """ Navigates the tree from a leaf node to the root, updating the win and visit count of each node along the path.

    Args:
        node:   A leaf node.
        won:    An indicator of whether the bot won or lost the game.

    """
    # 从叶子节点向根节点传播结果
    while node is not None:
        # 增加访问次数
        node.visits += 1
        
        # 如果胜利，增加胜利次数
        if won:
            node.wins += 1
        
        # 移动到父节点
        node = node.parent

def ucb(node: MCTSNode, is_opponent: bool):
    """ Calcualtes the UCB value for the given node from the perspective of the bot

    Args:
        node:   A node.
        is_opponent: A boolean indicating whether or not the last action was performed by the MCTS bot
    Returns:
        The value of the UCB function for the given node
    """
    # 如果节点未被访问过，返回无穷大
    if node.visits == 0:
        return float('inf')
    
    # 计算胜率
    win_rate = node.wins / node.visits
    
    # 如果是对手的节点，反转胜率
    if is_opponent:
        win_rate = 1 - win_rate
    
    # 如果父节点不存在或父节点访问次数为0，返回胜率
    if node.parent is None or node.parent.visits == 0:
        return win_rate
    
    # UCB1公式：平均奖励 + 探索项
    exploration = explore_faction * sqrt(log(node.parent.visits) / node.visits)
    
    return win_rate + exploration

def get_best_action(root_node: MCTSNode):
    """ Selects the best action from the root node in the MCTS tree

    Args:
        root_node:   The root node
    Returns:
        action: The best action from the root node
    
    """
    # 如果没有子节点，随机选择一个动作
    if not root_node.child_nodes:
        if root_node.untried_actions:
            return choice(root_node.untried_actions)
        else:
            return None
    
    # 选择访问次数最多的动作（robust child strategy）
    best_action = max(root_node.child_nodes.keys(), 
                     key=lambda action: root_node.child_nodes[action].visits)
    
    return best_action

def is_win(board: Board, state, identity_of_bot: int):
    # checks if state is a win state for identity_of_bot
    outcome = board.points_values(state)
    assert outcome is not None, "is_win was called on a non-terminal state"
    return outcome[identity_of_bot] == 1

def think(board: Board, current_state):
    """ Performs MCTS by sampling games and calling the appropriate functions to construct the game tree.

    Args:
        board:  The game setup.
        current_state:  The current state of the game.

    Returns:    The action to be taken from the current state

    """
    bot_identity = board.current_player(current_state) # 1 or 2
    root_node = MCTSNode(parent=None, parent_action=None, action_list=board.legal_actions(current_state))

    for _ in range(num_nodes):
        state = current_state
        node = root_node

        # 1. Selection: 遍历树直到找到可扩展的节点
        node, state = traverse_nodes(node, board, state, bot_identity)
        
        # 2. Expansion: 扩展叶子节点
        node, state = expand_leaf(node, board, state)
        
        # 3. Simulation: 使用启发式策略模拟到游戏结束
        terminal_state = rollout(board, state)
        
        # 4. Backpropagation: 将结果传播回根节点
        won = is_win(board, terminal_state, bot_identity)
        backpropagate(node, won)

    # Return an action, typically the most frequently used action (from the root) or the action with the best
    # estimated win rate.
    best_action = get_best_action(root_node)
    
    print(f"Modified MCTS Action chosen: {best_action}")
    return best_action
