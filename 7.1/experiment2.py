#!/usr/bin/env python3
"""
实验2：modified vs vanilla MCTS对比
测试启发式改进是否有效
"""

import sys
import json
from timeit import default_timer as time
import matplotlib.pyplot as plt
import p2_t3
import mcts_vanilla
import mcts_modified

# M1 Pro优化设置
GAMES_PER_TEST = 30  # 每种设置30场游戏
TREE_SIZES = [50, 100, 200]  # 三种不同树大小

class VariableTreeMCTS:
    """可变树大小的MCTS包装器"""
    def __init__(self, tree_size, use_modified=False):
        self.tree_size = tree_size
        self.use_modified = use_modified
        
    def think(self, board, state):
        if self.use_modified:
            # 使用modified版本
            original_num_nodes = mcts_modified.num_nodes
            mcts_modified.num_nodes = self.tree_size
            
            try:
                action = mcts_modified.think(board, state)
            finally:
                mcts_modified.num_nodes = original_num_nodes
        else:
            # 使用vanilla版本
            original_num_nodes = mcts_vanilla.num_nodes
            mcts_vanilla.num_nodes = self.tree_size
            
            try:
                action = mcts_vanilla.think(board, state)
            finally:
                mcts_vanilla.num_nodes = original_num_nodes
                
        return action

def run_experiment():
    """运行实验2"""
    print("实验2：Modified vs Vanilla MCTS对比")
    print(f"每种设置运行 {GAMES_PER_TEST} 场游戏")
    print("Player 1: Modified MCTS (启发式rollout)")
    print("Player 2: Vanilla MCTS (随机rollout)")
    print("-" * 50)
    
    board = p2_t3.Board()
    state0 = board.starting_state()
    
    results = {}
    
    for tree_size in TREE_SIZES:
        print(f"\n测试树大小: {tree_size}节点")
        
        # Player 1 使用Modified MCTS
        player1_mcts = VariableTreeMCTS(tree_size, use_modified=True)
        # Player 2 使用Vanilla MCTS
        player2_mcts = VariableTreeMCTS(tree_size, use_modified=False)
        
        wins = {'draw': 0, 1: 0, 2: 0}
        start_time = time()
        
        for game_num in range(GAMES_PER_TEST):
            if game_num % 5 == 0:
                print(f"  游戏 {game_num+1}/{GAMES_PER_TEST}...")
            
            state = state0
            current_player_func = player1_mcts.think
            
            # 限制最大回合数防止游戏过长
            max_moves = 81
            move_count = 0
            
            while not board.is_ended(state) and move_count < max_moves:
                action = current_player_func(board, state)
                state = board.next_state(state, action)
                move_count += 1
                
                # 切换玩家
                current_player_func = player2_mcts.think if current_player_func == player1_mcts.think else player1_mcts.think
            
            # 记录结果
            if board.is_ended(state):
                final_score = board.points_values(state)
                if final_score[1] == 1:
                    wins[1] += 1
                elif final_score[2] == 1:
                    wins[2] += 1
                else:
                    wins['draw'] += 1
            else:
                wins['draw'] += 1
        
        elapsed_time = time() - start_time
        
        # 计算Modified MCTS (Player 1)的胜率
        modified_wins = wins[1]
        modified_win_rate = modified_wins / GAMES_PER_TEST
        
        results[tree_size] = {
            'wins': wins,
            'modified_win_rate': modified_win_rate,
            'time': elapsed_time
        }
        
        print(f"  结果: Modified={wins[1]}, Vanilla={wins[2]}, 平局={wins['draw']}")
        print(f"  Modified MCTS胜率: {modified_win_rate:.2%}")
        print(f"  用时: {elapsed_time:.1f}秒")
    
    return results

def plot_results(results):
    """绘制结果图表"""
    # 配置中文字体支持
    import matplotlib
    matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
    matplotlib.rcParams['axes.unicode_minus'] = False
    
    tree_sizes = list(results.keys())
    win_rates = [results[size]['modified_win_rate'] for size in tree_sizes]
    
    plt.figure(figsize=(10, 6))
    plt.plot(tree_sizes, win_rates, 'ro-', linewidth=2, markersize=8, label='Modified MCTS')
    plt.axhline(y=0.5, color='gray', linestyle='--', alpha=0.7, label='50% (Equal)')
    
    plt.xlabel('Tree Size (Nodes)', fontsize=12)
    plt.ylabel('Modified MCTS Win Rate', fontsize=12)
    plt.title('Experiment 2: Modified vs Vanilla MCTS Performance', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)
    plt.legend()
    
    # 添加数据标签
    for x, y in zip(tree_sizes, win_rates):
        plt.annotate(f'{y:.2%}', (x, y), textcoords="offset points", 
                    xytext=(0,10), ha='center')
    
    plt.tight_layout()
    plt.savefig('experiment2_results.png', dpi=300, bbox_inches='tight')
    print("\n图表已保存为: experiment2_results.png")
    
    return plt

def save_results(results):
    """保存结果到JSON文件"""
    with open('experiment2_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print("结果已保存为: experiment2_results.json")

def analyze_improvement(results):
    """分析改进效果"""
    print("\n=== 改进效果分析 ===")
    
    overall_modified_wins = sum(data['wins'][1] for data in results.values())
    overall_vanilla_wins = sum(data['wins'][2] for data in results.values())
    overall_draws = sum(data['wins']['draw'] for data in results.values())
    total_games = sum(GAMES_PER_TEST for _ in results)
    
    overall_modified_rate = overall_modified_wins / total_games
    overall_vanilla_rate = overall_vanilla_wins / total_games
    
    print(f"总体结果 ({total_games}场游戏):")
    print(f"  Modified MCTS: {overall_modified_wins}胜 ({overall_modified_rate:.2%})")
    print(f"  Vanilla MCTS:  {overall_vanilla_wins}胜 ({overall_vanilla_rate:.2%})")
    print(f"  平局:          {overall_draws}场 ({overall_draws/total_games:.2%})")
    
    if overall_modified_rate > 0.6:
        print("\n✓ Modified MCTS显著优于Vanilla MCTS!")
    elif overall_modified_rate > 0.5:
        print("\n✓ Modified MCTS略优于Vanilla MCTS")
    elif overall_modified_rate > 0.4:
        print("\n- Modified MCTS与Vanilla MCTS表现相近")
    else:
        print("\n✗ Modified MCTS表现不如Vanilla MCTS")
    
    return {
        'overall_modified_rate': overall_modified_rate,
        'overall_vanilla_rate': overall_vanilla_rate,
        'improvement': overall_modified_rate - overall_vanilla_rate
    }

def main():
    print("实验2：启发式改进效果评估")
    print("=" * 50)
    
    try:
        results = run_experiment()
        
        print("\n=== 实验2总结 ===")
        for tree_size, data in results.items():
            print(f"树大小 {tree_size}: Modified胜率 {data['modified_win_rate']:.2%}, 用时 {data['time']:.1f}秒")
        
        analysis = analyze_improvement(results)
        
        save_results(results)
        plot_results(results)
        
        print("\n实验2完成！")
        print(f"总体改进幅度: {analysis['improvement']:+.2%}")
        
    except KeyboardInterrupt:
        print("\n实验被用户中断")
    except Exception as e:
        print(f"\n实验出错: {e}")

if __name__ == "__main__":
    main()