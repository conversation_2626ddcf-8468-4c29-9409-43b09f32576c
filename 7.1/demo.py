#!/usr/bin/env python3
"""
Ultimate Tic-Tac-Toe MCTS 演示程序
展示不同bot的对战效果
"""

import sys
from timeit import default_timer as time
import p2_t3
import mcts_vanilla
import mcts_modified
import random_bot
import rollout_bot

def quick_match(player1_name, player1_func, player2_name, player2_func, games=5):
    """快速对战演示"""
    print(f"\n🎮 {player1_name} vs {player2_name} ({games}场)")
    print("-" * 50)
    
    board = p2_t3.Board()
    state0 = board.starting_state()
    wins = {'draw': 0, 1: 0, 2: 0}
    
    start_time = time()
    
    for i in range(games):
        print(f"第{i+1}场: ", end="", flush=True)
        
        state = state0
        current_player_func = player1_func
        move_count = 0
        max_moves = 50  # 限制最大回合数
        
        while not board.is_ended(state) and move_count < max_moves:
            action = current_player_func(board, state)
            state = board.next_state(state, action)
            move_count += 1
            
            # 切换玩家
            current_player_func = player2_func if current_player_func == player1_func else player1_func
        
        # 记录结果
        if board.is_ended(state):
            final_score = board.points_values(state)
            if final_score[1] == 1:
                wins[1] += 1
                print(f"{player1_name}胜")
            elif final_score[2] == 1:
                wins[2] += 1
                print(f"{player2_name}胜")
            else:
                wins['draw'] += 1
                print("平局")
        else:
            wins['draw'] += 1
            print("超时平局")
    
    elapsed_time = time() - start_time
    
    # 显示结果
    print(f"\n📊 结果统计:")
    print(f"  {player1_name}: {wins[1]}胜 ({wins[1]/games:.1%})")
    print(f"  {player2_name}: {wins[2]}胜 ({wins[2]/games:.1%})")
    print(f"  平局: {wins['draw']}场 ({wins['draw']/games:.1%})")
    print(f"  用时: {elapsed_time:.1f}秒")

def demo_all_bots():
    """演示所有bot的对战"""
    print("🚀 Ultimate Tic-Tac-Toe MCTS 演示")
    print("=" * 60)
    
    # 定义所有bot
    bots = {
        "Random Bot": random_bot.think,
        "Rollout Bot": rollout_bot.think,
        "MCTS Vanilla": mcts_vanilla.think,
        "MCTS Modified": mcts_modified.think,
    }
    
    print("🤖 可用的Bot:")
    for i, name in enumerate(bots.keys(), 1):
        print(f"  {i}. {name}")
    
    # 关键对战演示
    demonstrations = [
        ("MCTS Modified", "MCTS Vanilla", "启发式 vs 标准MCTS"),
        ("MCTS Vanilla", "Rollout Bot", "MCTS vs 有限前瞻"),
        ("MCTS Modified", "Random Bot", "智能AI vs 随机选择"),
    ]
    
    for player1, player2, description in demonstrations:
        print(f"\n💡 {description}")
        quick_match(player1, bots[player1], player2, bots[player2])

def interactive_demo():
    """交互式演示"""
    bots = {
        "1": ("Random Bot", random_bot.think),
        "2": ("Rollout Bot", rollout_bot.think), 
        "3": ("MCTS Vanilla", mcts_vanilla.think),
        "4": ("MCTS Modified", mcts_modified.think),
    }
    
    print("\n🎯 交互式演示")
    print("选择两个Bot进行对战:")
    for key, (name, _) in bots.items():
        print(f"  {key}. {name}")
    
    try:
        p1 = input("\n选择Player 1 (1-4): ").strip()
        p2 = input("选择Player 2 (1-4): ").strip()
        
        if p1 in bots and p2 in bots:
            games = int(input("对战场数 (推荐5-10): ") or "5")
            
            player1_name, player1_func = bots[p1]
            player2_name, player2_func = bots[p2]
            
            quick_match(player1_name, player1_func, player2_name, player2_func, games)
        else:
            print("❌ 无效选择")
    except KeyboardInterrupt:
        print("\n👋 演示结束")
    except Exception as e:
        print(f"❌ 错误: {e}")

def show_algorithm_info():
    """显示算法信息"""
    print("\n📖 算法说明")
    print("=" * 40)
    print("""
🎲 Random Bot: 完全随机选择动作
📊 Rollout Bot: 对每个可能动作进行有限次随机模拟，选择平均分数最高的
🌳 MCTS Vanilla: 标准蒙特卡洛树搜索
   - Selection: UCB1策略选择节点
   - Expansion: 扩展未探索的动作
   - Simulation: 随机rollout到游戏结束
   - Backpropagation: 结果向上传播

🧠 MCTS Modified: 改进版MCTS
   - 启发式rollout策略:
     1. 优先选择能立即获胜的动作
     2. 优先阻止对手获胜
     3. 偏好中心位置和角落位置
     4. 其他情况随机选择
   - 调优的探索参数
   - 智能节点扩展策略
""")

def main():
    print("🎯 Ultimate Tic-Tac-Toe MCTS 演示系统")
    print("=" * 50)
    
    while True:
        print("\n🎪 选择演示模式:")
        print("  1. 自动演示 (预设对战)")
        print("  2. 交互式演示 (自选对手)")
        print("  3. 算法说明")
        print("  4. 退出")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            demo_all_bots()
        elif choice == "2":
            interactive_demo()
        elif choice == "3":
            show_algorithm_info()
        elif choice == "4":
            print("👋 再见!")
            break
        else:
            print("❌ 请输入1-4")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 演示结束，感谢使用!")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        print("请检查所有文件是否在正确位置")