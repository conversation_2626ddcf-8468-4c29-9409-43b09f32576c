#!/usr/bin/env python3
"""
实验1：不同树大小的vanilla MCTS对比
Player 1 固定100节点，Player 2 测试不同树大小
"""

import sys
import json
from timeit import default_timer as time
import matplotlib.pyplot as plt
import p2_t3
import mcts_vanilla
import random_bot

# 为了M1 Pro性能优化，减少游戏数量
GAMES_PER_TEST = 50  # 从100减少到50以提高速度
TREE_SIZES = [25, 50, 100, 200, 400]  # 测试不同的树大小

class VariableTreeMCTS:
    """可变树大小的MCTS包装器"""
    def __init__(self, tree_size):
        self.tree_size = tree_size
        
    def think(self, board, state):
        # 临时修改num_nodes
        original_num_nodes = mcts_vanilla.num_nodes
        mcts_vanilla.num_nodes = self.tree_size
        
        try:
            action = mcts_vanilla.think(board, state)
        finally:
            # 恢复原始值
            mcts_vanilla.num_nodes = original_num_nodes
            
        return action

def run_experiment():
    """运行实验1"""
    print("开始实验1：不同树大小的vanilla MCTS对比")
    print(f"每种设置运行 {GAMES_PER_TEST} 场游戏")
    print("Player 1 (固定): 100节点 MCTS")
    print("Player 2 (可变): 不同节点数的 MCTS")
    print("-" * 50)
    
    board = p2_t3.Board()
    state0 = board.starting_state()
    
    results = {}
    
    # Player 1 固定为100节点的MCTS
    player1_mcts = VariableTreeMCTS(100)
    
    for tree_size in TREE_SIZES:
        print(f"\n测试Player 2树大小: {tree_size}节点")
        
        # Player 2 使用当前树大小
        player2_mcts = VariableTreeMCTS(tree_size)
        
        wins = {'draw': 0, 1: 0, 2: 0}
        start_time = time()
        
        for game_num in range(GAMES_PER_TEST):
            if game_num % 10 == 0:
                print(f"  游戏 {game_num+1}/{GAMES_PER_TEST}...")
            
            state = state0
            current_player_func = player1_mcts.think
            
            while not board.is_ended(state):
                action = current_player_func(board, state)
                state = board.next_state(state, action)
                
                # 切换玩家
                current_player_func = player2_mcts.think if current_player_func == player1_mcts.think else player1_mcts.think
            
            # 记录结果
            final_score = board.points_values(state)
            if final_score[1] == 1:
                wins[1] += 1
            elif final_score[2] == 1:
                wins[2] += 1
            else:
                wins['draw'] += 1
        
        elapsed_time = time() - start_time
        
        # 计算Player 2的胜率
        player2_wins = wins[2]
        player2_win_rate = player2_wins / GAMES_PER_TEST
        
        results[tree_size] = {
            'wins': wins,
            'player2_win_rate': player2_win_rate,
            'time': elapsed_time
        }
        
        print(f"  结果: P1={wins[1]}, P2={wins[2]}, 平局={wins['draw']}")
        print(f"  Player 2胜率: {player2_win_rate:.2%}")
        print(f"  用时: {elapsed_time:.1f}秒")
    
    return results

def plot_results(results):
    """绘制结果图表"""
    # 配置中文字体支持
    import matplotlib
    matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans', 'Arial']
    matplotlib.rcParams['axes.unicode_minus'] = False
    
    tree_sizes = list(results.keys())
    win_rates = [results[size]['player2_win_rate'] for size in tree_sizes]
    
    plt.figure(figsize=(10, 6))
    plt.plot(tree_sizes, win_rates, 'bo-', linewidth=2, markersize=8)
    plt.xlabel('Player 2 Tree Size (Nodes)', fontsize=12)
    plt.ylabel('Player 2 Win Rate', fontsize=12)
    plt.title('Experiment 1: MCTS Performance vs Tree Size\\n(Player 1 Fixed at 100 Nodes)', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)
    
    # 添加数据标签
    for x, y in zip(tree_sizes, win_rates):
        plt.annotate(f'{y:.2%}', (x, y), textcoords="offset points", 
                    xytext=(0,10), ha='center')
    
    plt.tight_layout()
    plt.savefig('experiment1_results.png', dpi=300, bbox_inches='tight')
    print("\n图表已保存为: experiment1_results.png")
    
    return plt

def save_results(results):
    """保存结果到JSON文件"""
    with open('experiment1_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print("结果已保存为: experiment1_results.json")

def main():
    print("实验1：树大小对MCTS性能的影响")
    print("=" * 50)
    
    try:
        results = run_experiment()
        
        print("\n=== 实验1总结 ===")
        for tree_size, data in results.items():
            print(f"树大小 {tree_size}: Player 2胜率 {data['player2_win_rate']:.2%}, 用时 {data['time']:.1f}秒")
        
        save_results(results)
        plot_results(results)
        
        print("\n实验1完成！")
        
    except KeyboardInterrupt:
        print("\n实验被用户中断")
    except Exception as e:
        print(f"\n实验出错: {e}")

if __name__ == "__main__":
    main()