#!/usr/bin/env python3
"""
修复图表中文乱码问题
重新生成带有正确字体设置的图表
"""

import json
import matplotlib
import matplotlib.pyplot as plt

# 配置中文字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans', 'Arial']
matplotlib.rcParams['axes.unicode_minus'] = False

def fix_experiment1_chart():
    """修复实验1图表"""
    try:
        # 读取实验1结果
        with open('experiment1_results.json', 'r') as f:
            results = json.load(f)
        
        tree_sizes = [int(size) for size in results.keys()]
        win_rates = [results[str(size)]['player2_win_rate'] for size in tree_sizes]
        
        plt.figure(figsize=(10, 6))
        plt.plot(tree_sizes, win_rates, 'bo-', linewidth=2, markersize=8)
        plt.xlabel('Player 2 Tree Size (Nodes)', fontsize=12)
        plt.ylabel('Player 2 Win Rate', fontsize=12)
        plt.title('Experiment 1: MCTS Performance vs Tree Size\n(Player 1 Fixed at 50 Nodes)', fontsize=14)
        plt.grid(True, alpha=0.3)
        plt.ylim(0, 1)
        
        # 添加数据标签
        for x, y in zip(tree_sizes, win_rates):
            plt.annotate(f'{y:.2%}', (x, y), textcoords="offset points", 
                        xytext=(0,10), ha='center', fontsize=10)
        
        plt.tight_layout()
        plt.savefig('experiment1_results_fixed.png', dpi=300, bbox_inches='tight')
        print("✅ 实验1图表已修复: experiment1_results_fixed.png")
        plt.close()
        
    except FileNotFoundError:
        print("❌ 未找到experiment1_results.json文件")
    except Exception as e:
        print(f"❌ 修复实验1图表时出错: {e}")

def fix_experiment2_chart():
    """修复实验2图表"""
    try:
        # 读取实验2结果
        with open('experiment2_results.json', 'r') as f:
            results = json.load(f)
        
        tree_sizes = [int(size) for size in results.keys()]
        win_rates = [results[str(size)]['modified_win_rate'] for size in tree_sizes]
        
        plt.figure(figsize=(10, 6))
        plt.plot(tree_sizes, win_rates, 'ro-', linewidth=2, markersize=8, label='Modified MCTS')
        plt.axhline(y=0.5, color='gray', linestyle='--', alpha=0.7, label='50% (Equal)')
        
        plt.xlabel('Tree Size (Nodes)', fontsize=12)
        plt.ylabel('Modified MCTS Win Rate', fontsize=12)
        plt.title('Experiment 2: Modified vs Vanilla MCTS Performance', fontsize=14)
        plt.grid(True, alpha=0.3)
        plt.ylim(0, 1)
        plt.legend()
        
        # 添加数据标签
        for x, y in zip(tree_sizes, win_rates):
            plt.annotate(f'{y:.2%}', (x, y), textcoords="offset points", 
                        xytext=(0,10), ha='center', fontsize=10)
        
        plt.tight_layout()
        plt.savefig('experiment2_results_fixed.png', dpi=300, bbox_inches='tight')
        print("✅ 实验2图表已修复: experiment2_results_fixed.png")
        plt.close()
        
    except FileNotFoundError:
        print("❌ 未找到experiment2_results.json文件")
    except Exception as e:
        print(f"❌ 修复实验2图表时出错: {e}")

def create_summary_chart():
    """创建实验结果对比图"""
    try:
        # 读取两个实验的结果
        with open('experiment1_results.json', 'r') as f:
            exp1_results = json.load(f)
        
        # 创建对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 实验1图表
        tree_sizes_1 = [int(size) for size in exp1_results.keys()]
        win_rates_1 = [exp1_results[str(size)]['player2_win_rate'] for size in tree_sizes_1]
        
        ax1.plot(tree_sizes_1, win_rates_1, 'bo-', linewidth=2, markersize=8)
        ax1.set_xlabel('Player 2 Tree Size (Nodes)', fontsize=12)
        ax1.set_ylabel('Player 2 Win Rate', fontsize=12)
        ax1.set_title('Experiment 1: Tree Size Impact\n(vs Fixed 50-node Player)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1)
        
        # 添加数据标签
        for x, y in zip(tree_sizes_1, win_rates_1):
            ax1.annotate(f'{y:.1%}', (x, y), textcoords="offset points", 
                        xytext=(0,10), ha='center', fontsize=9)
        
        # 实验2图表（如果存在）
        try:
            with open('experiment2_results.json', 'r') as f:
                exp2_results = json.load(f)
            
            tree_sizes_2 = [int(size) for size in exp2_results.keys()]
            win_rates_2 = [exp2_results[str(size)]['modified_win_rate'] for size in tree_sizes_2]
            
            ax2.plot(tree_sizes_2, win_rates_2, 'ro-', linewidth=2, markersize=8, label='Modified MCTS')
            ax2.axhline(y=0.5, color='gray', linestyle='--', alpha=0.7, label='Equal (50%)')
            ax2.set_xlabel('Tree Size (Nodes)', fontsize=12)
            ax2.set_ylabel('Modified MCTS Win Rate', fontsize=12)
            ax2.set_title('Experiment 2: Modified vs Vanilla\n(Equal Tree Sizes)', fontsize=12)
            ax2.grid(True, alpha=0.3)
            ax2.set_ylim(0, 1)
            ax2.legend()
            
            # 添加数据标签
            for x, y in zip(tree_sizes_2, win_rates_2):
                ax2.annotate(f'{y:.1%}', (x, y), textcoords="offset points", 
                            xytext=(0,10), ha='center', fontsize=9)
                
        except FileNotFoundError:
            ax2.text(0.5, 0.5, 'Experiment 2\nResults Not Available', 
                    transform=ax2.transAxes, ha='center', va='center',
                    fontsize=14, alpha=0.6)
            ax2.set_title('Experiment 2: Not Completed', fontsize=12)
        
        plt.tight_layout()
        plt.savefig('experiments_summary.png', dpi=300, bbox_inches='tight')
        print("✅ 实验对比图已生成: experiments_summary.png")
        plt.close()
        
    except Exception as e:
        print(f"❌ 创建对比图时出错: {e}")

def main():
    print("🎨 修复图表中文乱码问题")
    print("=" * 40)
    
    print("\n检查可用的matplotlib字体...")
    available_fonts = [f.name for f in matplotlib.font_manager.fontManager.ttflist]
    chinese_fonts = [f for f in available_fonts if any(keyword in f for keyword in ['SimHei', 'Arial Unicode', 'PingFang', 'Heiti'])]
    
    if chinese_fonts:
        print(f"✅ 找到中文字体: {chinese_fonts[:3]}")
    else:
        print("⚠️  未找到中文字体，将使用英文标签")
    
    print("\n开始修复图表...")
    fix_experiment1_chart()
    fix_experiment2_chart()
    create_summary_chart()
    
    print("\n🎉 图表修复完成！")
    print("生成的文件:")
    print("  - experiment1_results_fixed.png")
    print("  - experiment2_results_fixed.png (如果实验2已完成)")
    print("  - experiments_summary.png")

if __name__ == "__main__":
    main()