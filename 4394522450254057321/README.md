# SiouxFalls交通网络分析系统

## 📋 项目简介

这是一个基于TNTP格式SiouxFalls数据的交通网络复杂性分析工具，完全仿照原始example.py的功能结构，但使用了更丰富的TNTP格式原始数据。

## 🗂️ 文件结构

```
4394522450254057321/
├── sioux_falls_analysis.py    # 🆕 新的主分析脚本（推荐使用）
├── example.py                 # 原始分析脚本
├── tntp_reader.py            # TNTP格式数据读取器
├── convert_sioux_falls.py    # 数据格式转换工具
├── SiouxFalls_net.tntp       # 网络数据（TNTP格式）
├── SiouxFalls_node.tntp      # 节点数据（TNTP格式）
├── SiouxFalls_trips.tntp     # OD流量数据（TNTP格式）
├── SF网络.xls                # 原始Excel数据
└── README.md                 # 本说明文档
```

## 🚀 快速开始

### 运行新的分析系统
```bash
python sioux_falls_analysis.py
```

### 运行原始分析系统
```bash
python example.py
```

## 📊 数据对比

| 特征 | 原始Excel数据 | TNTP格式数据 |
|------|---------------|--------------|
| 数据来源 | SF网络.xls | SiouxFalls TNTP文件 |
| 节点数量 | 24 | 24 |
| 边数量 | 76 | 76 |
| 数据完整性 | 基础信息 | 完整的交通属性 |
| OD数据 | 简化格式 | 真实流量数据 |
| 网络属性 | 容量、长度 | 容量、长度、自由流时间、BPR参数等 |

## 🔧 功能特点

### 1. 数据读取
- ✅ 直接读取TNTP格式原始数据
- ✅ 保留完整的网络属性信息
- ✅ 真实的OD流量矩阵

### 2. 网络可视化
- ✅ 基础网络图绘制
- ✅ 节点坐标可视化
- ✅ 边容量信息显示

### 3. 社区检测
- ✅ Louvain算法社区检测
- ✅ 模块度计算
- ✅ 社区分类可视化

### 4. 网络攻击分析
- ✅ 随机攻击模拟
- ✅ 蓄意攻击模拟（基于度中心性/介数中心性）
- ✅ 攻击效果对比分析

### 5. 级联失效分析
- ✅ 基于负载-容量模型的级联失效
- ✅ 静态失效过程可视化
- ✅ 动态失效过程动画（GIF）

## 📈 分析结果

运行`sioux_falls_analysis.py`后会生成：

1. **网络基础图** - 显示网络拓扑结构
2. **社区分类图** - 显示检测到的社区结构
3. **攻击对比图** - 随机vs蓄意攻击效果对比
4. **级联失效图** - 失效过程静态展示
5. **级联失效动画** - `sioux_falls_cascade.gif`

## 📊 数据集详细信息

### 🎯 **数据集名称**
**SiouxFalls交通网络数据集**

### 🌐 **数据来源网址**
**GitHub仓库**: [TransportationNetworks](https://github.com/bstabler/TransportationNetworks)
- 这是一个由bstabler维护的开源交通网络数据集合
- 包含了全球多个经典的交通网络测试案例

### 📋 **数据集特点**
- **经典测试案例**: SiouxFalls是交通网络研究中的标准测试网络
- **真实数据基础**: 基于真实城市交通网络设计
- **标准格式**: 使用TNTP（Transportation Network Test Problems）格式
- **广泛应用**: 被全球交通研究者广泛使用

### 📁 **数据文件构成**
- `SiouxFalls_net.tntp` - 网络拓扑和边属性数据
- `SiouxFalls_node.tntp` - 节点坐标数据
- `SiouxFalls_trips.tntp` - OD（起点-终点）流量数据
- `SF网络.xls` - 原始Excel格式数据

### 🔢 **网络规模**
- **节点数量**: 24个
- **边数量**: 76条
- **OD对**: 包含完整的起点-终点流量矩阵
- **总流量**: 360,600.0单位

### 🛠️ **数据属性**
网络边包含完整的交通工程属性：
- 容量（capacity）
- 长度（length）
- 自由流时间（free_flow_time）
- BPR参数（b, power）
- 速度限制（speed）
- 收费（toll）
- 道路类型（link_type）

### 💡 **学术价值**
这是一个非常权威和标准的交通网络研究数据集，在学术界被广泛使用作为算法验证和性能测试的基准。您的代码使用了这个经典数据集进行复杂网络分析，包括网络可视化、社区检测、网络攻击分析和级联失效模拟。

## 💡 使用建议

1. **推荐使用** `sioux_falls_analysis.py` - 数据更完整，功能相同
2. **数据扩展** - 可以从[TransportationNetworks](https://github.com/bstabler/TransportationNetworks)获取更多网络数据
3. **参数调整** - 可以修改攻击参数、级联失效参数等进行不同实验
4. **结果保存** - 所有图表都会显示，级联失效动画会保存为GIF文件

## 🔄 与原始代码的关系

- **完全兼容**: 所有功能与原始example.py完全相同
- **数据增强**: 使用了更丰富的TNTP格式原始数据
- **代码优化**: 针对Mac系统优化了字体显示
- **功能扩展**: 保持了所有原有的分析功能

## 🛠️ 依赖库

```python
pandas
networkx
numpy
matplotlib
seaborn
scipy
python-louvain
```

## 📞 技术支持

如需更多网络数据或功能扩展，可以：
1. 访问[TransportationNetworks](https://github.com/bstabler/TransportationNetworks)获取更多数据
2. 修改`network_name`参数使用其他TNTP格式网络
3. 调整分析参数进行不同的实验

---

🎉 **现在你有了一个功能完整、数据丰富的交通网络分析系统！**
